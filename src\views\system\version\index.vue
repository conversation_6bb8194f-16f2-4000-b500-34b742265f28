<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="版本号" prop="version">
        <el-input v-model="queryParams.version" placeholder="请输入版本号" clearable @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item label="版本编号" prop="versionCode">
        <el-input v-model="queryParams.versionCode" placeholder="请输入版本编号" clearable @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <!-- <el-form-item label="强制更新" prop="isForce">
        <el-select v-model="queryParams.isForce" placeholder="请选择是否强制更新" clearable class="!w-180px">
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="类型" prop="appType">
        <el-select v-model="queryParams.appType" placeholder="请选择类型" clearable class="!w-180px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.APP_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['system:version:create']">
          <Icon icon="ep:plus" class="mr-5px" />
          新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading" v-hasPermi="['system:version:export']">
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="版本号" align="center" prop="version" />
      <el-table-column label="版本编号" align="center" prop="versionCode" />
      <!-- <el-table-column label="二维码" align="center">
        <template #default="scope">
          <el-image :src="scope.row.qrCodeImg" preview-teleported hide-on-click-modal :preview-src-list="[scope.row.qrCodeImg]" style="width: 40px; height: 40px" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="APK下载地址" align="center" prop="apkUrl" :show-overflow-tooltip="true" /> -->
      <el-table-column label="更新内容" align="center" prop="updateContent" :show-overflow-tooltip="true" />
      <!-- <el-table-column label="强制更新" align="center" prop="isForce">
        <template #default="scope">
          <el-tag v-if="scope.row.isForce == 1" type="success">是</el-tag>
          <el-tag v-else type="warning">否</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="类型" align="center" prop="appType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.APP_TYPE" :value="scope.row.appType" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="安装包大小" align="center" prop="size" :formatter="fileSizeFormatter" /> -->
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)" v-hasPermi="['system:version:update']">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['system:version:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AppVersionForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts" name="AppVersion">
import QRCode from 'qrcode'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as AppVersionApi from '@/api/system/version'
import AppVersionForm from './AppVersionForm.vue'
import { fileSizeFormatter } from '@/utils'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const generateQRCode = async (link: string) => {
  return await QRCode.toDataURL(link)
}

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list: any = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  version: null,
  versionCode: null,
  isForce: null,
  appType: null
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// const showQrCode = (link: string) => {
//   const qrCodeModal = ref();
//   qrCodeModal.value.open(link);
// };

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AppVersionApi.getAppVersionPage(queryParams)
    // const qrCodePromises = data.list.map(async (s: any) => {
    //   s.qrCodeImg = await generateQRCode(s.apkUrl)
    //   return s
    // })
    // // 使用 Promise.all 等待所有 Promise 完成
    // list.value = await Promise.all(qrCodePromises)

    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AppVersionApi.deleteAppVersion(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AppVersionApi.exportAppVersion(queryParams)
    download.excel(data, '版本管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
