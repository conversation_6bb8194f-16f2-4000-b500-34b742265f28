<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable @keyup.enter="handleQuery"
            class="!w-150px" />
        </el-form-item>
        <!-- <el-form-item label="是否AI学习" prop="isStudy">
          <el-select v-model="queryParams.isStudy" placeholder="请选择是否AI学习" clearable class="!w-150px">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="文件来源" prop="source">
          <el-select v-model="queryParams.source" placeholder="请选择文件来源" clearable class="!w-150px">
            <el-option label="文档翻译" value="1" />
            <el-option label="人工上传" value="2" />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="创建时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
            start-placeholder="开始日期" end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
        </el-form-item> -->
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table ref="table" v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
        @selection-change="selectionChange" :select-on-indeterminate="false">
        <el-table-column type="selection" width="55" />
        <el-table-column label="文件名称" align="center" prop="fileName" width="200px" />
        <el-table-column label="原文链接" align="center" prop="origin" />
        <el-table-column label="译文链接" align="center" prop="translate" />
        <!-- <el-table-column label="当前版本" align="center" prop="version" /> -->
        <el-table-column label="文件来源" align="center" prop="source">
          <template #default="scope">
            <el-tag type="primary">
              {{ scope.row.source === 1 ? '文档翻译' : '人工上传' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否学习" align="center" prop="isStudy">
          <template #default="scope">
            <el-tag :type="scope.row.isStudy === 1 ? 'success' : 'danger'">
              {{ scope.row.isStudy === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      </el-table>
      <!-- 分页 -->
      <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
        @pagination="getList" />
    </ContentWrap>
    <p class="tips color-red-4">注：未勾选以上版本，则视为新增版本。</p>
    <template #footer>
      <p class="dialog-footer text-center">
        <el-button type="primary" @click="toConfirmSelect">
          {{ curSelectedId ? '确认选择该版本' : '新增版本' }}
        </el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </p>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TranslateVersionApi, TranslateVersionVO } from '@/api/doc/translateversion'
import { ElMessageBox } from 'element-plus'

const props = defineProps({
  transId: {
    type: [Number, String],
    default: '',
  },
})

/** 翻译版本信息 列表 */
defineOptions({ name: 'SaveVersion' })

const dialogVisible = ref(false);
const dialogTitle = ref('上传版本');

const open = () => {
  dialogVisible.value = true;
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const previewWordRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref<TranslateVersionVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fileName: undefined,
  isStudy: undefined,
  source: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 选择现有版本
const curSelectedId = ref(null);
const { proxy } = getCurrentInstance()!;
const selectionChange = (selection) => {
  curSelectedId.value = selection[selection.length - 1]?.id;
  const latestSelection = selection[selection.length - 1];
  list.value.forEach(row => {
    if (row !== latestSelection) {
      proxy.$refs.table.toggleRowSelection(row, false);
    }
  });
}

const toConfirmSelect = () => {
  const data = {
    transId: props.transId,
    id: curSelectedId.value || null,
  }
  const title = curSelectedId.value ? '确认选择该版本？' : '确认新增版本？'
  // 移除监听器
  ElMessageBox.confirm(title, '提示', {
    confirmButtonText: '确 认',
    cancelButtonText: '取 消'
  })
    .then(async () => {
      await TranslateVersionApi.createTranslateVersion(data)
      curSelectedId.value = null;
      dialogVisible.value = false;
      message.success(t('common.createSuccess'))
    })
    .catch(() => console.info('操作取消'))
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TranslateVersionApi.getTranslateVersionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const previewFile = async (row: any) => {
  if (row) {
    previewWordRef.value.open(row);
  }

}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TranslateVersionApi.deleteTranslateVersion(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TranslateVersionApi.exportTranslateVersion(queryParams)
    download.excel(data, '翻译版本信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const changeSwitch = async (obj) => {
  const index = obj.$index;
  const id = obj.row.id;
  const isStudy = obj.row.isStudy;
  try {
    await TranslateVersionApi.updateTranslateVersion({
      id,
      isStudy,
    })
    message.success(t('common.updateSuccess'))
  } catch (error) {
    obj.row.isStudy = isStudy === 1 ? 0 : 1
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>