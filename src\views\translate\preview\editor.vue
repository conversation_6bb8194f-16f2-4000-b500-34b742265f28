<template>
  <div class="preview-box-editor">
    <p class="title">{{ t('translate.editor.modifyTranslation') }} - {{ currentDate }} - {{ curId }}</p>
    <p class="showTable" @click="toShowTable">{{ t('translate.editor.historyRecords') }} &gt;&gt;</p>
    <el-space v-if="loading" direction="horizontal" wrap fill :fill-ratio="40" style="width: 100%">
      <div class="space-item">
        <el-skeleton animated :rows="20" :count="1" />
      </div>
      <div class="space-item space-item2">
        <el-skeleton animated :rows="20" :count="1" />
      </div>
    </el-space>
    <div class="showPreview" v-else>
      <div class="show-pdf" v-if="pdfUrlList[0]">
        <PdfPreview :pdfUrlList="pdfUrlList" />
      </div>
      <div class="show-word" v-else-if="wordUrlList[0]">
        <WordPreviewEditor :wordUrlList="wordUrlList" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DocTranslateApi } from "@/api/system/translate";
import PdfPreview from "@/components/PdfPreview/index.vue";
import WordPreviewEditor from "@/components/WordPreview/index_editor.vue";
import { useI18n } from 'vue-i18n';

defineOptions({ name: "TranslatePreview" });

const { t } = useI18n();
const router = useRouter();
const message = useMessage(); // 消息弹窗

const loading = ref(true); // 列表的加载中

const currentDate = new Date().toDateString();
const curId: any = router.currentRoute.value.query.id || "";

const pdfUrlList = ref(["", ""]);
const wordUrlList = ref(["", ""]);
const excelUrlList = ref(["", ""]);

/** 查询详情 */
const fetchData = async () => {
  loading.value = true;
  try {
    const data = await DocTranslateApi.getDocTranslate(curId);
    // 根据文件名获取文件类型
    const fileType = data.fileName.split(".").pop();
    console.log(fileType);
    if (fileType === "pdf") {
      pdfUrlList.value = [
        data.fileUrlOrigin || "",
        data.fileUrlTranslate || "",
        // "https://hengrui.obs.cidc-rp-2046.lygrmxchcso1.joint.cmecloud.cn/379be2a7c5e0cc5b1e783c1370395ce94889d1f7801d6049eff09017d3fe5cf7.pdf",
        // "https://hengrui.obs.cidc-rp-2046.lygrmxchcso1.joint.cmecloud.cn/379be2a7c5e0cc5b1e783c1370395ce94889d1f7801d6049eff09017d3fe5cf7.pdf",
        // 'http://36.138.43.233:9090/file/b6d9bddf5a665694a89a8e2bc4715a9ed09e085d09b3b5b5223e6a741f44c954.pdf',
        // 'http://36.138.43.233:9090/file/b6d9bddf5a665694a89a8e2bc4715a9ed09e085d09b3b5b5223e6a741f44c954.pdf'
      ];
    } else if (fileType === "docx") {
      wordUrlList.value = [data.fileUrlOrigin || "", data.fileUrlTranslate || ""];
    } else if (fileType === "xlsx") {
      excelUrlList.value = [data.fileUrlOrigin || "", data.fileUrlTranslate || ""];
    } else {
      message.error(t('translate.editor.unsupportedFileType'));
    }
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.preview-box-editor {
  width: 100%;
  min-height: 800px;
  color: #1d2129;
  background: #fff;
  border: 1px solid #f2f3f5;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgb(35 46 67 / 6%);
  padding: 30px;

  .title{
    padding: 20px 0;
  }

  .showTable {
    position: absolute;
    right: 70px;
    top: 30px;
    color: #2988f4;
    font-size: 16px;
    text-align: right;
    padding: 20px 30px;
    cursor: pointer;

    &:hover {
      color: #083e7b;
    }
  }
  .space-item {
    width: 50%;
    padding: 0 20px 0 0;
    border-right: 1px solid #f2f3f5;

    &.space-item2 {
      border: none;
      padding: 0 0 0 10px;
    }
  }
}
</style>
