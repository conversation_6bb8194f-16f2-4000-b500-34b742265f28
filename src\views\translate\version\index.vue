<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
      <el-form-item :label="t('translate.version.fileName')" prop="fileName">
        <el-input v-model="queryParams.fileName" :placeholder="t('translate.version.fileNamePlaceholder')" clearable
          @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.version.aiLearning')" prop="isStudy">
        <el-select v-model="queryParams.isStudy" :placeholder="t('translate.version.selectAiLearning')" clearable
          class="!w-240px">
          <el-option :label="t('translate.version.yes')" value="1" />
          <el-option :label="t('translate.version.no')" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('translate.version.fileSource')" prop="source">
        <el-select v-model="queryParams.source" :placeholder="t('translate.version.selectFileSource')" clearable
          class="!w-240px">
          <el-option :label="t('translate.version.documentTranslation')" value="1" />
          <el-option :label="t('translate.version.manualUpload')" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('translate.version.createTime')" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          :start-placeholder="t('translate.version.startDate')" :end-placeholder="t('translate.version.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> {{ t('translate.version.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> {{ t('translate.version.reset') }}
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['system:translate:version:create']">
          <Icon icon="ep:plus" class="mr-5px" /> {{ t('translate.version.add') }}
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:translate:version:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('translate.version.fileName')" align="center" prop="fileName" />
      <el-table-column :label="t('translate.version.originalLink')" align="center" prop="origin" />
      <el-table-column :label="t('translate.version.translationLink')" align="center" prop="translate" />
      <!-- <el-table-column :label="t('translate.version.currentVersion')" align="center" prop="version" /> -->
      <el-table-column :label="t('translate.version.fileSource')" align="center" prop="source">
        <template #default="scope">
          <el-tag type="primary">
            {{ scope.row.source === 1 ? t('translate.version.documentTranslation') : t('translate.version.manualUpload')
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.version.currentVersion')" align="center" prop="version">
        <template #default="scope">
          <el-tag type="primary">
            v{{ scope.row.version }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.version.aiLearning')" align="center" prop="isStudy">
        <template #default="scope">

          <el-switch v-model="scope.row.isStudy" class="ml-2" inline-prompt
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
            :active-text="t('translate.version.yes')" :inactive-text="t('translate.version.no')" :active-value="1"
            :inactive-value="0" size="large" @change="changeSwitch(scope)" />

          <!-- <el-tag :type="scope.row.isStudy === 1 ? 'success' : 'danger'">
            {{ scope.row.isStudy === 1 ? t('translate.version.yes') : t('translate.version.no') }}
          </el-tag> -->
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.version.createTime')" align="center" prop="createTime"
        :formatter="dateFormatter" width="180px" />
      <el-table-column :label="t('translate.version.actions')" align="center" min-width="120px">

        <template #default="scope">
          <el-button link type="success" @click="previewFile(scope.row)"
            v-hasPermi="['system:translate:version:query']">
            {{ t('translate.version.preview') }}
          </el-button>
          <el-button link type="primary" @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:translate:version:update']">
            {{ t('translate.version.edit') }}
          </el-button>
          <el-button link type="warning" @click="showHistory(scope.row)"
            v-hasPermi="['system:translate:history:query']">
            {{ t('translate.version.historyVersion') }}
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:translate:version:delete']">
            {{ t('translate.version.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TranslateVersionForm ref="formRef" @success="getList" />
  <WordPreviewByUrl ref="previewWordRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TranslateVersionApi, TranslateVersionVO } from '@/api/doc/translateversion'
import TranslateVersionForm from './versionForm.vue'
import WordPreviewByUrl from '@/components/WordPreview/index_preview.vue'

const router = useRouter()

/** 翻译版本信息 列表 */
defineOptions({ name: 'TranslateVersion' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TranslateVersionVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fileName: undefined,
  isStudy: undefined,
  source: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TranslateVersionApi.getTranslateVersionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

// 预览
const previewWordRef = ref()
const previewFile = async (row: any) => {
  if (row) {
    previewWordRef.value.open(row);
  }
}

const showHistory = (row: any) => {
  router.push({
    path: '/translate/translate-version-history',
    query: {
      versionId: row.id,
      transId: row.transId,
      fileName: row.fileName,
    }
  })
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TranslateVersionApi.deleteTranslateVersion(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TranslateVersionApi.exportTranslateVersion(queryParams)
    download.excel(data, t('translate.version.exportFileName'))
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const changeSwitch = async (obj) => {
  const index = obj.$index;
  const id = obj.row.id;
  const isStudy = obj.row.isStudy;
  try {
    await TranslateVersionApi.updateTranslateVersion({
      id,
      isStudy,
    })
    message.success(t('common.updateSuccess'))
  } catch (error) {
    obj.row.isStudy = isStudy === 1 ? 0 : 1
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>