import request from "@/config/axios";

// 文档信息 VO
export interface StatisticsVO {
  type: string; // AI文件类别
  startTime: string
  endTime: string// 原因
}

// 文档信息 API
export const StatisticsApi = {
  // 新增文档信息
  getDocStatistics: async (data: StatisticsVO) => {
    return await request.post({ url: `/system/statistics/getDocStatistics`, data });
  },
  getTransData: async (data: StatisticsVO) => {
    return await request.post({ url: `/system/statistics/getTransData`, data });
  },
  getAiAnsData: async (data: StatisticsVO) => {
    return await request.post({ url: `/system/statistics/getAiAnsData`, data });
  },
  getTransDaysNum: async (data: StatisticsVO) => {
    return await request.post({ url: `/system/statistics/getTransDaysNum`, data });
  },
  getAiAnsDaysNum: async (data: StatisticsVO) => {
    return await request.post({ url: `/system/statistics/getAiAnsDaysNum`, data });
  },
};
