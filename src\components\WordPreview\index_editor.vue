<template>
  <div class="word-preview" v-loading="state.loading">
    <div class="word-flex-box">
      <div class="word-flex-wrap">
        <div
          class="word-wrap"
          :style="{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: `translate(-50%,-50%) scale(${state.scale})`,
            width: `${pageWidth}`,
            height: `${pageHeight}`,
          }"
        >
          <word-preview-single :currentPage="state.pageNum" :fileUrl="state.source_origin" class="vue-word-embed" />
        </div>
      </div>
      <div class="word-flex-wrap">
        <div
          class="word-wrap"
          :style="{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: `translate(-50%,-50%) scale(${state.scale})`,
            width: `${pageWidth}`,
            height: `${pageHeight}`,
          }"
        >
          <word-preview-single-editor :currentPage="state.pageNum" :fileUrl="state.source_translate" class="vue-word-embed" />
        </div>
      </div>
    </div>
<!--    <div class="page-tool">-->
<!--      <div class="page-tool-item active">整篇预览</div>-->
<!--      <div class="page-tool-item" @click="jumpTo(1)">逐段对照</div>-->
<!--      <div class="page-tool-item" @click="jumpTo(2)">逐句对照</div>-->
<!--      &lt;!&ndash; <div class="page-tool-item" @click="lastPage">上一页</div>-->
<!--      <div class="page-tool-item" @click="nextPage">下一页</div> &ndash;&gt;-->
<!--      &lt;!&ndash; <div class="page-tool-item">{{ state.pageNum }}/{{ state.numPages }}</div> &ndash;&gt;-->
<!--      <div class="page-tool-item" @click="pageZoomOut">放大</div>-->
<!--      <div class="page-tool-item" @click="pageZoomIn">缩小</div>-->
<!--      <div class="page-tool-item" @click="pageRest">重置缩放</div>-->
<!--      <div class="page-tool-item" @click="downloadPDF">下载</div>-->
<!--    </div>-->
  </div>
</template>
<script lang="ts" setup>
import WordPreviewSingle from "./WordPreviewSingle.vue";
import WordPreviewSingleEditor from "./WordPreviewSingle_editor.vue";

const props = defineProps({
  wordUrlList: {
    type: Array,
    required: true,
  },
});
const router = useRouter();
const { id } = router.currentRoute.value.query;

const jumpTo = (type: number) => {
  if (type === 1) {
    router.push({ path: "/translate/preview/text", query: { id } });
  } else if (type === 2) {
    router.push({ path: "/translate/preview/sentence", query: { id } });
  }
};

const state = reactive({
  source_origin: props.wordUrlList[0], // 预览pdf文件地址
  source_translate: props.wordUrlList[1], // 预览pdf文件地址
  pageNum: 1, //当前页面
  scale: 1, // 缩放比例
  numPages: 0, // 总页数
  loading: "", //加载效果
});
// 下载pdf
function downloadPDF() {
  fetch(encodeURI(props.wordUrlList[1])).then((res) => {
    res.blob().then((myBlob) => {
      const href = URL.createObjectURL(myBlob);
      const a = document.createElement("a");
      a.href = href;
      a.download = "report.docx"; // 下载文件重命名，并指定文件扩展名为 ".word"
      document.body.appendChild(a); // 将<a>元素添加到文档中，以便进行点击下载
      a.click();
      document.body.removeChild(a); // 下载完成后移除<a>元素
    });
  });
}

// onMounted(() => {
  // state.loading = true; // 添加一个loading状态
// });

const pageHeight = ref("100%");
const pageWidth = ref("100%");
function lastPage() {
  if (state.pageNum > 1) {
    state.pageNum -= 1;
  }
}

function nextPage() {
  if (state.pageNum < state.numPages) {
    state.pageNum += 1;
  }
}

function pageZoomOut() {
  if (state.scale < 2) {
    state.scale += 0.1;
    pageHeight.value = parseInt(pageHeight.value) - 5.0 + "%";
    pageWidth.value = parseInt(pageWidth.value) - 5.0 + "%";
  }
}

function pageZoomIn() {
  if (state.scale > 1) {
    state.scale -= 0.1;
    pageHeight.value = parseInt(pageHeight.value) + 5.0 + "%";
    pageWidth.value = parseInt(pageWidth.value) + 5.0 + "%";
  }
}

const pageRest = () => {
  state.scale = 1;
  pageHeight.value = "100%";
  pageWidth.value = "100%";
};
</script>
<style lang="scss" scoped>
.word-preview {
  height: 100%;
  position: relative;
}
.word-flex-box {
  height: 100%;
  display: flex;
  gap: 10px;
  justify-content: space-between;
}
.word-flex-wrap {
  height: 100%;
  min-height: 800px;
  flex: 1;
  padding: 20px 0;
  position: relative;
  background-color: #e9e9e9;
  box-sizing: border-box;
  overflow: auto;
  border-radius: 8px;
}
.word-wrap {
  // overflow-y: auto;
}

.vue-word-embed {
  text-align: center;
  width: 100%;
  border: 1px solid #e5e5e5;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-tool {
  height: 35px;
  width: 480px;
  margin: 0 auto;
  display: flex;
  margin-top: 20px;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  user-select: none;
  z-index: 2;
  background: rgba(66, 66, 66, 0.9);
  color: white;
  cursor: pointer;
}

.page-tool-item {
  cursor: pointer;
  padding: 0 10px;
  height: 100%;
  line-height: 35px;
  font-size: 14px;

  &.active,
  &:hover,
  &:focus,
  &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
</style>
