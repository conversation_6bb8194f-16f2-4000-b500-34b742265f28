<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="110px">
      <!-- <el-form-item label="文件名称" prop="fileName">
        <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item> -->
      <!-- <el-form-item label="是否AI学习" prop="isStudy">
        <el-select v-model="queryParams.isStudy" placeholder="请选择是否AI学习" clearable class="!w-240px">
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item> -->
      <el-form-item :label="t('translate.versionHistory.fileSource')" prop="source">
        <el-select v-model="queryParams.source" :placeholder="t('translate.versionHistory.selectFileSource')" clearable class="!w-240px">
          <el-option :label="t('translate.versionHistory.documentTranslation')" value="1" />
          <el-option :label="t('translate.versionHistory.manualUpload')" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('translate.versionHistory.createTime')" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          :start-placeholder="t('translate.versionHistory.startDate')" :end-placeholder="t('translate.versionHistory.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> {{ t('translate.versionHistory.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> {{ t('translate.versionHistory.reset') }}
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['system:translate:version:create']">
          <Icon icon="ep:plus" class="mr-5px" /> {{ t('translate.versionHistory.add') }}
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:translate:version:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('translate.versionHistory.fileName')" align="center">
        {{ fileName }}
      </el-table-column>
      <el-table-column :label="t('translate.versionHistory.originalLink')" align="center" prop="origin" />
      <el-table-column :label="t('translate.versionHistory.translationLink')" align="center" prop="translate" />
      <el-table-column :label="t('translate.versionHistory.fileSource')" align="center" prop="source">
        <template #default="scope">
          <el-tag type="primary">
            {{ scope.row.source === 1 ? t('translate.versionHistory.documentTranslation') : t('translate.versionHistory.manualUpload') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.versionHistory.currentVersion')" align="center" prop="version">
        <template #default="scope">
          <el-tag type="primary">
            v{{ scope.row.version }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="是否学习" align="center" prop="isStudy">
        <template #default="scope">

          <el-switch v-model="scope.row.isStudy" class="ml-2" inline-prompt
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949" active-text="是" inactive-text="否"
            :active-value="1" :inactive-value="0" size="large" @change="changeSwitch(scope)" />

          <el-tag :type="scope.row.isStudy === 1 ? 'success' : 'danger'">
            {{ scope.row.isStudy === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column :label="t('translate.versionHistory.createTime')" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column :label="t('translate.versionHistory.actions')" align="center" min-width="120px">

        <template #default="scope">
          <el-button link type="success" @click="previewFile(scope.row)"
            v-hasPermi="['system:translate:version:query']">
            {{ t('translate.versionHistory.preview') }}
          </el-button>
          <el-button link type="primary" @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:translate:version:update']">
            {{ t('translate.versionHistory.edit') }}
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:translate:version:delete']">
            {{ t('translate.versionHistory.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <VersionHistoryForm ref="formRef" @success="getList" />
  <WordPreviewByUrl ref="previewWordRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TranslateHistoryVersionApi, TranslateHistoryVersionVO } from '@/api/doc/translateversion/history'
import VersionHistoryForm from './versionHistoryForm.vue'
import WordPreviewByUrl from '@/components/WordPreview/index_preview.vue'

/** 翻译版本信息 列表 */
defineOptions({ name: 'TranslateVersionHistory' })

const router = useRouter();
const { versionId, transId, fileName } = router.currentRoute.value.query;

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const previewWordRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref<TranslateHistoryVersionVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  versionId,
  pageNo: 1,
  pageSize: 10,
  fileName: undefined,
  isStudy: undefined,
  source: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TranslateHistoryVersionApi.getTranslateHistoryVersionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const previewFile = async (row: any) => {
  if (row) {
    previewWordRef.value.open({ ...row, fileName });
  }

}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, cId?: number) => {
  formRef.value.open(type, cId, versionId, transId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TranslateHistoryVersionApi.deleteTranslateHistoryVersion(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TranslateHistoryVersionApi.exportTranslateHistoryVersion(queryParams)
    download.excel(data, t('translate.versionHistory.exportFileName'))

  } catch {
  } finally {
    exportLoading.value = false
  }
}

// const changeSwitch = async (obj) => {
//   const index = obj.$index;
//   const id = obj.row.id;
//   const isStudy = obj.row.isStudy;
//   try {
//     await TranslateHistoryVersionApi.updateTranslateHistoryVersion({
//       id,
//       isStudy,
//     })
//     message.success(t('common.updateSuccess'))
//   } catch (error) {
//     obj.row.isStudy = isStudy === 1 ? 0 : 1
//   }
// }

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>