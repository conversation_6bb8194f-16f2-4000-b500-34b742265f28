<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :width="800" :close-on-click-modal="false" destory-on-close>
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="80px">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.record.original')" prop="origin">
            <el-input v-model="formData.origin" :placeholder="t('translate.record.original')" type="textarea"
              autosize />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.record.translate')" prop="translate">
            <el-input v-model="formData.translate" :placeholder="t('translate.record.translate')" type="textarea"
              autosize />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-button class="retranslate" type="primary" size="mini" @click="handleRetranslate" :loading="retranslateLoading">
              <Icon icon="ep:refresh" class="mr-5px" />
              重新翻译
            </el-button>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">{{ t('common.submit') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('common.close') }}</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { FormRules } from "element-plus";
import { DocTranslateApi } from "@/api/system/translate";
import { useEmit } from "@/hooks/web/useEmitt";
import { useI18n } from 'vue-i18n';

defineOptions({ name: "ReportEditForm" });

const props = defineProps({
  whichPannel: {
    type: String,
    default: "right",
  },
});

const { t } = useI18n();
const message = useMessage(); // 消息弹窗

const dialogVisible = ref(false); // 弹窗的是否展示
const dialogTitle = ref(computed(() => t('translate.record.translateHistoryRecord'))); // 弹窗的标题
const formLoading = ref(true);
const formData = ref({
  id: "",
  origin: "",
  translate: "",
});


const resetForm = () => {
  formData.value = {
    id: "",
    origin: "",
    translate: "",
  };
};

const formRules = reactive<FormRules>({
  origin: [
    {
      required: true,
      message: t('common.noEmpty'),
      trigger: "blur",
    },
  ],
  translate: [
    {
      required: true,
      message: t('common.noEmpty'),
      trigger: "blur",
    },
  ],
});
const formRef = ref(); // 表单 Ref
const router = useRouter();
// const curId = computed(() => router.currentRoute.value.query?.id);
/** 打开弹窗 */
const open = async (id: number) => {
  resetForm();
  dialogVisible.value = true;
  formLoading.value = true;
  const data = await DocTranslateApi.getRecordTranslate(id);
  formData.value = data;
  formLoading.value = false;
};

defineExpose({ open }); // 提供 open 方法，用于打开弹窗

// // 重新翻译
// const retranslateFormData = ref({
//   from: "zh",
//   to: "en",
//   content: "",
//   isCache: true,
// });
// const retranslateLoading = ref(false);
// const handleRetranslate = async () => {
//   if (!formData.value.origin) return;
//   retranslateLoading.value = true;
//   retranslateFormData.value.content = formData.value.origin;
//   await DocTranslateApi.textTranslate(retranslateFormData.value).then((res) => {
//     formData.value.translation = res?.content || '';
//   });
//   retranslateLoading.value = false;
// };

/** 提交表单 */
const emit = defineEmits(["success"]); // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  if (!formData.value.id) {
    message.error(t('common.idError'));
    return;
  }
  // 校验表单
  if (!formRef) return;
  const valid = await formRef.value.validate();
  console.log(valid)
  if (!valid) return;
  // 提交请求
  formLoading.value = true;
  try {
    const data: any = formData.value;
    await DocTranslateApi.updateRecordTranslate(data);
    message.success(t('common.submitSuccess'));
    dialogVisible.value = false;
    // 发送操作成功的事件
    // emit("success", props.whichPannel === "left" ? data.originChange : data.translationChange);
    emit("success");
  } finally {
    formLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
.origin-text {
  color: #999;
  line-height: 20px;
  font-size: 14px;
  border: 1px solid #eee;
  padding: 5px 11px;
  border-radius: 4px;
}
</style>
