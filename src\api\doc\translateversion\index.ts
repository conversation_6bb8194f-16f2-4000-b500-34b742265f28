import request from '@/config/axios'

// 翻译版本信息 VO
export interface TranslateVersionVO {
  id: number // 主键ID
  transId: number // 文档翻译ID
  fileName: string // 文件名称
  origin: string // 原文链接
  translate: string // 当前译文地址
  isStudy: string // 是否学习
  version: number // 当前版本
  source: number // 来源：1：文档翻译；2：人工上传
}

// 翻译版本信息 API
export const TranslateVersionApi = {
  // 查询翻译版本信息分页
  getTranslateVersionPage: async (params: any) => {
    return await request.get({ url: `/system/trans/version/page`, params })
  },

  // 查询翻译版本信息详情
  getTranslateVersion: async (id: number) => {
    return await request.get({ url: `/system/trans/version/get?id=` + id })
  },

  // 新增翻译版本信息
  createTranslateVersion: async (data: TranslateVersionVO) => {
    return await request.post({ url: `/system/trans/version/create`, data })
  },

  // 修改翻译版本信息
  updateTranslateVersion: async (data: TranslateVersionVO) => {
    return await request.put({ url: `/system/trans/version/update`, data })
  },

  // 删除翻译版本信息
  deleteTranslateVersion: async (id: number) => {
    return await request.delete({ url: `/system/trans/version/delete?id=` + id })
  },

  // 导出翻译版本信息 Excel
  exportTranslateVersion: async (params) => {
    return await request.download({ url: `/system/trans/version/export-excel`, params })
  },
}
