import { useI18n } from '@/hooks/web/useI18n'
import { getMenuTitle } from '@/utils/menuI18n'

/**
 * 菜单国际化 Hook
 * 提供统一的菜单标题翻译功能
 */
export const useMenuI18n = () => {
  const { t } = useI18n()

  /**
   * 获取菜单标题的翻译文本
   * @param titleKey 标题键
   * @returns 翻译后的文本
   */
  const getTranslatedMenuTitle = (titleKey: string): string => {
    return getMenuTitle(titleKey, t)
  }

  return {
    t,
    getTranslatedMenuTitle
  }
}
