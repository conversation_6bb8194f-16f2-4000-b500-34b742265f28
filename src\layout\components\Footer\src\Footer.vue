<script lang="ts" setup>
import { useAppStore } from "@/store/modules/app";
import { useDesign } from "@/hooks/web/useDesign";
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { LayoutType } from '@/types/layout'

const { wsCache } = useCache()

// eslint-disable-next-line vue/no-reserved-component-names
defineOptions({ name: "Footer" });

const appStore = useAppStore();

const title = computed(() => appStore.getTitle);

const curYear = new Date().getFullYear();
const { getPrefixCls } = useDesign();

// 菜单折叠
const collapse = computed(() => appStore.getCollapse);

// 是否是移动端
const mobile = computed(() => appStore.getMobile);

const layout = computed(() => wsCache.get(CACHE_KEY.LAYOUT))

const prefixCls = computed(() => {
  const footerPrefixCls = getPrefixCls("footer");
  // 位置和宽度
  let leftAndWidth = '';
  // console.log(layout.value)
  if (layout.value === 'top' || layout.value === 'classic' || mobile.value) {
     leftAndWidth = 'left-0 w-[100%]';
  } else if (layout.value === 'cutMenu') { 
    if (collapse.value) {
      leftAndWidth = 'w-[calc(100%-var(--tab-menu-min-width))] left-[var(--tab-menu-min-width)]'
    } else {
      leftAndWidth = 'w-[calc(100%-var(--tab-menu-max-width))] left-[var(--tab-menu-max-width)]'
    }
  } else {
    if (collapse.value) {
      leftAndWidth ="w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)]";
    } else {
      leftAndWidth = "w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)]";
    }
  }
  return `${footerPrefixCls} ${leftAndWidth}`;
});

</script>

<template>
  <div
    :class="prefixCls"
    class="position-absolute w-[100%] bottom-0px text-center leading-[var(--app-footer-height)] text-[var(--el-text-color-placeholder)] dark:bg-[var(--el-bg-color)] bg-[var(--app-content-bg-color)] z-10"
  >
    <p class="text-13px text-center color-gray">Copyright ©{{ curYear }}-{{ title }}</p>
  </div>
</template>
