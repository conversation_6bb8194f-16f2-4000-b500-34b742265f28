<template>
  <div :class="uniqueClass">
    <div v-if="loading" class="loading">Loading...</div>
    <div v-else-if="htmlContent" v-html="styledHtmlContent"></div>
    <p v-else>请提供有效的文件地址。</p>
  </div>

  <download-word ref="downloadFileByStyleAndHtml" />
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from "vue";
import mammoth from "mammoth";
import DownloadWord from "@/components/DownloadTextFile/wordByStyleHtml.vue";

interface Props {
  fileUrl: string;
  fileName?: string;
}

const uniqueClass = `docx-preview-${Math.random().toString(36).substring(2, 9)}`; // 生成唯一的类名
const props = defineProps<Props>();
const styledHtmlContent = ref<string | null>(null);
const loading = ref<boolean>(false); // 添加加载状态
const htmlContent = ref<string | null>(null);
const downloadFileByStyleAndHtml = ref();

const styledContent = `
  <style>
    body {
      background-color: white;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .${uniqueClass} {
      background-color: white;
      width: 210mm; /* 设置为 A4 纸宽度 */
      margin: auto; /* 居中对齐 */
      padding: 50px; /* 页边距 */
      box-sizing: border-box; /* 确保边距不会影响宽度计算 */
    }
    table {
      width: 100%; /* 表格宽度100% */
      border-collapse: collapse;
      table-layout: fixed; /* 固定表格布局 */
    }
    th, td {
      border: 1px solid #ccc;
      padding: 8px;
      text-align: left;
      overflow: hidden; /* 防止内容溢出 */
      white-space: normal; /* 允许换行 */
      word-wrap: break-word; /* 强制换行 */
    }
    th {
      background-color: #f2f2f2;
    }
    img {
      max-width: 100%; /* 图片宽度最大为100% */
      height: auto; /* 高度自适应 */
    }
  </style>
`;

// 监视 fileUrl 的变化
watch(
  () => props.fileUrl,
  () => {
    loadFile();
  }
);

// 读取并转换 DOCX 文件为 HTML
const loadFile = async () => {
  loading.value = true; // 开始加载
  if (props.fileUrl) {
    try {
      const response = await fetch(props.fileUrl);
      const arrayBuffer = await response.arrayBuffer();
      const { value } = await mammoth.convertToHtml({ arrayBuffer });
      htmlContent.value = value;
      applyStyles();
    } catch (error) {
      console.error("加载文件出错:", error);
      htmlContent.value = null;
    } finally {
      loading.value = false; // 加载完成
    }
  } else {
    htmlContent.value = null;
    loading.value = false; // 加载完成
  }
};

// 应用样式以增强表格的显示
const applyStyles = () => {
  if (htmlContent.value) {
    styledHtmlContent.value = `
      ${styledContent}
      <div class="show-word-content" contenteditable="true">
        ${htmlContent.value}
      </div>
    `;
    // console.log(styledHtmlContent)
  }
};

const downloadFile = () => {
  const ext = props.fileName.split('.').pop()?.toLowerCase();
  let fileName = props.fileName.replace(`.${ext}`, `（译文）.${ext}`);

  downloadFileByStyleAndHtml.value.downloadFile(fileName, styledContent, uniqueClass);
};

defineExpose({ downloadFile });

onMounted(() => {
  loadFile();
});
</script>

<style scoped>
.docx-preview {
  border: 1px solid #ccc;
  border-radius: 5px;
}
.loading {
  margin-top: 30%;
  font-size: 40px;
  text-align: center;
}
</style>
