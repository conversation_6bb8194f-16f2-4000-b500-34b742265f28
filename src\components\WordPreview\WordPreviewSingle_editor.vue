<template>
    <div :class="uniqueClass">
        <div>
            <el-upload
                    action=""
                    accept=".docx"
                    class="upload-demo"
                    :before-upload="beforeUpload"
                    :multiple="false"
                    :show-file-list="false"
                    :limit="1">
                <el-button type="primary">上传word</el-button>
            </el-upload>
            <h3>修改历史:</h3>
            <ul>
                <li v-for="(item, index) in history" :key="index">
                    <strong>修改前：</strong> {{ item.before }}<br/>
                    <strong>修改后：</strong> {{ item.after }}
                </li>
            </ul>
        </div>
        <div v-if="loading" class="loading">Loading...</div>
<!--        <div v-else-if="htmlContent" v-html="styledHtmlContent"></div>-->
        <div
                ref="editableDiv"
                contenteditable="true"
                @focus="handleFocus"
                @blur="handleBlur"
                @input="handleInput"
                @compositionstart="isComposing = true"
                @compositionend="handleCompositionEnd"
                @keyup="handleKeyUp"
                @click="handleKeyUp"
        ></div>
<!--        <p v-else>请提供有效的文件地址。</p>-->
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, nextTick } from 'vue';
import mammoth from "mammoth";

interface Props {
    fileUrl: string;
}

// 定义历史记录的类型
interface HistoryItem {
    before: string;
    after: string;
}

const props = defineProps<Props>();
const htmlContent = ref<string | null>(null);
const styledHtmlContent = ref<string | null>(null);
const loading = ref<boolean>(false); // 添加加载状态
const uniqueClass = `docx-preview-${Math.random().toString(36).substr(2, 9)}`; // 生成唯一的类名

// 监视 fileUrl 的变化
watch(() => props.fileUrl, () => {
    loadFile();
});

// 读取并转换 DOCX 文件为 HTML
const loadFile = async () => {
    loading.value = true; // 开始加载
    if (props.fileUrl) {
        try {
            const response = await fetch(props.fileUrl);
            const arrayBuffer = await response.arrayBuffer();
            const { value } = await mammoth.convertToHtml({ arrayBuffer });
            htmlContent.value = value;
            applyStyles();
        } catch (error) {
            console.error('加载文件出错:', error);
            htmlContent.value = null;
        } finally {
            loading.value = false; // 加载完成
        }
    } else {
        htmlContent.value = null;
        loading.value = false; // 加载完成
    }
};

// 应用样式以增强表格的显示
const applyStyles = () => {
    if (htmlContent.value) {
        styledHtmlContent.value = `
      <style>
        body {
          background-color: white;
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
        }
        .${uniqueClass} {
          background-color: white;
          width: 210mm; /* 设置为 A4 纸宽度 */
          margin: auto; /* 居中对齐 */
          padding: 50px; /* 页边距 */
          box-sizing: border-box; /* 确保边距不会影响宽度计算 */
        }
        table {
          width: 100%; /* 表格宽度100% */
          border-collapse: collapse;
          table-layout: fixed; /* 固定表格布局 */
        }
        th, td {
          border: 1px solid #ccc;
          padding: 8px;
          text-align: left;
          overflow: hidden; /* 防止内容溢出 */
          white-space: normal; /* 允许换行 */
          word-wrap: break-word; /* 强制换行 */
        }
        th {
          background-color: #f2f2f2;
        }
        img {
          max-width: 100%; /* 图片宽度最大为100% */
          height: auto; /* 高度自适应 */
        }
      </style>
      ${htmlContent.value}
    `;
    }
};

const isComposing = ref<boolean>(false);  // 标志是否正在使用输入法
const previousContent = ref<string>("");  // 保存用户修改前的内容
const currentParagraph = ref<HTMLElement | null>(null);  // 记录当前段落
const history = reactive<HistoryItem[]>([]);  // 存储修改历史
const isDirty = ref<boolean>(false); // 标记当前段落是否有变化

// 获取 DOM 引用
const editableDiv = ref<HTMLElement | null>(null);

// 获取光标所在段落
const getActiveParagraph = (): HTMLElement | null => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
        let node = selection.getRangeAt(0).commonAncestorContainer;
        return node.nodeType === 3 ? (node.parentNode as HTMLElement) : (node as HTMLElement);
    }
    return null;
};

// 处理段落变化
const handleParagraphChange = () => {
    const activeParagraph = getActiveParagraph();
    if (activeParagraph && activeParagraph !== currentParagraph.value) {
        // 如果光标切换到了不同段落
        if (currentParagraph.value && isDirty.value) {
            // 记录离开段落的修改后内容
            history.push({
                before: previousContent.value,  // 记录进入段落时的内容
                after: currentParagraph.value.innerHTML,  // 记录离开段落时的内容
            });
        }

        // 更新当前段落为新段落
        currentParagraph.value = activeParagraph;
        previousContent.value = activeParagraph.innerHTML; // 更新初始内容
        isDirty.value = false;  // 清除修改标志，刚进入新段落
    }
};

// 处理聚焦事件
const handleFocus = () => {
    handleParagraphChange();  // 检查段落变化
};

// 处理失焦事件，记录段落内容的变化
const handleBlur = () => {
    const activeParagraph = currentParagraph.value;
    if (previousContent.value !== activeParagraph.innerHTML) {
        // 记录修改历史
        history.push({
            before: previousContent.value,  // 记录进入段落时的内容
            after: activeParagraph.innerHTML, // 记录当前段落离开时的内容
        });
    }
    currentParagraph.value = null; // 清空当前段落
};

// 处理输入事件，标记段落内容已经被修改
const handleInput = () => {
    if (!isComposing.value) {
        isDirty.value = true; // 标记段落有修改
    }
};

// 处理拼音输入结束
const handleCompositionEnd = () => {
    isComposing.value = false;
    handleInput();  // 输入法结束时，处理段落修改标记
};

// 处理键盘抬起事件和点击事件，检查光标是否切换段落
const handleKeyUp = () => {
    handleParagraphChange();  // 手动触发段落变化检查
};


//  暂用：文件跨域，暂用文件上传代替测试

const beforeUpload = async (file) => {
    if (file.size !== 0) {
        //解析word
        htmlContent.value = await analysisWord(file);
        applyStyles();
        editableDiv.value.innerHTML = styledHtmlContent.value
    }
}
//解析word
const analysisWord = (file) => {
    return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = function (evt) {//当文件读取完毕后
            mammoth//调用mammoth组件的方法进行解析文件
                .convertToHtml({ arrayBuffer: evt.target.result })
                .then(function (resultObject) {
                    resolve(resultObject.value);//将处理好的html数据返回
                })
                .catch((e) => {
                })
            ;
        }
        reader.readAsArrayBuffer(file);
    })
}
</script>
