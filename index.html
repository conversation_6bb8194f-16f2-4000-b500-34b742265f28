<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <link rel="icon" href="/favicon.ico"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta
            name="keywords"
            content="恒瑞大模型平台"
    />
    <meta
            name="description"
            content="恒瑞大模型平台"
    />
    <title>%VITE_APP_TITLE%</title>
</head>
<body>
<div id="app">
    <style>
        .app-loading {
            display: flex;
            width: 100%;
            height: 100%;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            background: #f0f2f5;
        }

        :deep(.el-menu--horizontal) {
            justify-content: end !important;
        }

        .app-loading .app-loading-wrap {
            position: absolute;
            top: 50%;
            left: 50%;
            display: flex;
            -webkit-transform: translate3d(-50%, -50%, 0);
            transform: translate3d(-50%, -50%, 0);
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .app-loading .app-loading-title {
            margin-bottom: 30px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .app-loading .app-loading-logo {
            width: 100px;
            margin: 0 auto 15px auto;
        }

        .app-loading .app-loading-item {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 60px;
            vertical-align: middle;
            border-radius: 50%;
        }

        .app-loading .app-loading-outter {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 4px solid #2d8cf0;
            border-bottom: 0;
            border-left-color: transparent;
            border-radius: 50%;
            animation: loader-outter 1s cubic-bezier(0.42, 0.61, 0.58, 0.41) infinite;
        }

        .app-loading .app-loading-inner {
            position: absolute;
            top: calc(50% - 20px);
            left: calc(50% - 20px);
            width: 40px;
            height: 40px;
            border: 4px solid #87bdff;
            border-right: 0;
            border-top-color: transparent;
            border-radius: 50%;
            animation: loader-inner 1s cubic-bezier(0.42, 0.61, 0.58, 0.41) infinite;
        }

        @-webkit-keyframes loader-outter {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @keyframes loader-outter {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @-webkit-keyframes loader-inner {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(-360deg);
                transform: rotate(-360deg);
            }
        }

        @keyframes loader-inner {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(-360deg);
                transform: rotate(-360deg);
            }
        }
    </style>
    <div class="app-loading">
        <div class="app-loading-wrap">
            <div class="app-loading-title">
                <img src="/logo.gif" class="app-loading-logo" alt="Logo"/>
                <div class="app-loading-title">%VITE_APP_TITLE%</div>
            </div>
            <div class="app-loading-item">
                <div class="app-loading-outter"></div>
                <div class="app-loading-inner"></div>
            </div>
        </div>
    </div>
</div>
<script type="module" src="/src/main.ts"></script>
</body>
</html>
