<template>
  <div class="auth-login-box">
    <p class="login-title">恒瑞大模型平台</p>
    <div class="corner top-left"></div>
    <div class="corner top-right"></div>
    <div class="corner bottom-left"></div>
    <div class="corner bottom-right"></div>
    <div class="login-loading" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0)"></div>
    <p class="login-form">登录认证中...</p>
  </div>
</template>
<script lang="ts" name="AuthLoginForm" setup>
const loading = ref(true);
</script>

<style lang="scss" scoped>
.auth-login-box {
  width: 400px;
  height: 400px;
  position: relative;
  background: rgba(34, 78, 145, 0.6);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);

  &::before {
    content: "";
    position: absolute;
    top: -1px;
    left: 25%;
    right: 25%;
    width: 50%;
    height: 3px;
    background: linear-gradient(to right, rgba(23, 146, 213, 0) 0, rgba(23, 146, 213, 0.9) 30%, rgba(157, 226, 244, 0.9) 50%, rgba(23, 146, 213, 0.9) 70%, rgba(23, 146, 213, 0) 100%);
  }

  &::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 25%;
    right: 25%;
    width: 50%;
    height: 3px;
    background: linear-gradient(to right, rgba(23, 146, 213, 0) 0, rgba(23, 146, 213, 0.9) 30%, rgba(157, 226, 244, 0.9) 50%, rgba(23, 146, 213, 0.9) 70%, rgba(23, 146, 213, 0) 100%);
  }

  // background: url('../../../assets/imgs/bg.png') center / contain no-repeat;

  .corner {
    position: absolute;
    width: 20px;
    height: 20px;
    &::before,
    &::after {
      content: "";
      position: absolute;
      background: #02a5ef;
    }
    &.top-left {
      top: 0;
      left: 0;
      &::before {
        left: 0;
        top: 0;
        height: 4px;
        width: 20px;
      }
      &::after {
        left: 0;
        top: 0;
        width: 4px;
        height: 20px;
      }
    }
    &.top-right {
      top: 0;
      right: 0;
      &::before {
        right: 0;
        top: 0;
        height: 4px;
        width: 20px;
      }
      &::after {
        right: 0;
        top: 0;
        width: 4px;
        height: 20px;
      }
    }
    &.bottom-left {
      bottom: 0;
      left: 0;
      &::before {
        left: 0;
        bottom: 0;
        height: 4px;
        width: 20px;
      }
      &::after {
        left: 0;
        bottom: 0;
        width: 4px;
        height: 20px;
      }
    }
    &.bottom-right {
      bottom: 0;
      right: 0;
      &::before {
        right: 0;
        bottom: 0;
        height: 4px;
        width: 20px;
      }
      &::after {
        right: 0;
        bottom: 0;
        width: 4px;
        height: 20px;
      }
    }
  }

  .login-title {
    color: #fff;
    text-align: center;
    font-size: 30px;
    font-weight: 700;
    margin: 20px 0;
  }

  .login-loading {
    width: 200px;
    height: 150px;
    margin: 0 auto;
    transform: scale(1.5);
  }

  .login-form {
    width: 100%;
    text-align: center;
    color: rgb(64, 158, 255);
    font-size: 18px;
  }
}
</style>
