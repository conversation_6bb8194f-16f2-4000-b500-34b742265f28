<template>
  <div class="preview-box">
    <p class="title">{{ t('translate.textPreview.paragraphEdit') }}</p>
    <el-popover
      placement="bottom-start"
      :width="315"
      trigger="hover"
    >
      <template #reference>
        <p class="showShare" @click="showShare">{{ t('translate.textPreview.share') }}</p>
      </template>
      <el-form :model="form" class="demo-form-inline">
        <el-form-item :label="t('translate.textPreview.shareUser')">
          <el-select
            style="width: 150px"
            v-model="form.value1"
            :placeholder="t('translate.textPreview.selectUser')"
            clearable
          >
            <el-option label="Zone one" value="shanghai" />
            <el-option label="Zone two" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('translate.textPreview.sharePermission')">
          <el-radio-group v-model="form.value2">
            <el-radio value="Sponsor">{{ t('translate.textPreview.viewOnly') }}</el-radio>
            <el-radio value="Venue">{{ t('translate.textPreview.viewAndDownload') }}</el-radio>
            <el-radio value="Venue">{{ t('translate.textPreview.editable') }}</el-radio>
            <el-radio value="Venue">{{ t('translate.textPreview.editAndDownload') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary">{{ t('translate.textPreview.confirm') }}</el-button>
        </el-form-item>
      </el-form>
    </el-popover>
    <p class="showTable" @click="toShowTable">{{ t('translate.textPreview.historyRecords') }} &gt;&gt;</p>
    <p class="state">
      {{ t('translate.textPreview.auditStatus') }}：
      <dict-tag :type="DICT_TYPE.AUDIT_STATUS" :value="auditStatus" size="large" />
    </p>
    <el-space v-if="loading" direction="horizontal" wrap fill :fill-ratio="40" style="width: 100%">
      <div class="space-item">
        <el-skeleton animated :rows="20" :count="1" />
      </div>
      <div class="space-item space-item2">
        <el-skeleton animated :rows="20" :count="1" />
      </div>
    </el-space>
    <div class="showTranslationText" v-else>
      <TextPreview ref="textPreview" :textList="textList" :curId="curId" :fileName="fileName" :fileUrl="fileUrl" @open-history="openHistory" />
    </div>
    <HistoryTable ref="historyTableRef" />
    <p v-hasPermi="['system:translate:submit']" v-if="!loading && (auditStatus == 0 || auditStatus == 3)" class="showSave" @click="toShowSave">全部保存</p>
    <p v-hasPermi="['system:translate:audit']" v-if="!loading && auditStatus == 1" class="showCheck" @click="toShowCheck">{{ t('translate.textPreview.audit') }}</p>
  </div>
  <AuditForm ref="auditForm" />
</template>

<script setup lang="ts">
import { DICT_TYPE } from "@/utils/dict";
import type { Action } from "element-plus";
import { ElMessageBox } from "element-plus";
import TextPreview from "@/components/TextPreview/index.vue";
import HistoryTable from "./historyTable.vue";
import { DocTranslateApi } from "@/api/system/translate/index";
import AuditForm from "./components/AuditForm.vue";
import { useI18n } from 'vue-i18n';

defineOptions({ name: "TranslatePreviewText" });

const { t } = useI18n();
const router = useRouter();
const message = useMessage(); // 消息弹窗

const loading = ref(true); // 列表的加载中

const curId: any = router.currentRoute.value?.query?.id || "";
const fileName = ref("");
const fileUrl = ref("");
const auditStatus = ref();
const textList: any = ref([]);

onMounted(async () => {
  loading.value = false;

  try {
    const detail = await DocTranslateApi.getDocTranslate(curId);
    fileName.value = detail.fileName;
    fileUrl.value = detail.fileUrlTranslate;
    auditStatus.value = detail.auditStatus;
  } catch (error) {
    console.log(error);
  }

  setTimeout(async () => {
    const list = await DocTranslateApi.getDocSplit(curId);
    // 设置组
    // for (let i = 0; i < list.length; i += 100) {
    //   const temp = list.slice(i, i + 100);
    //   textList.value.push(temp);
    // }
    textList.value = [list];
  }, 1000);
});

const historyTableRef = ref();
const toShowTable = () => {
  historyTableRef.value.open(curId);
};

const form = ref({
  value1: '',
  value2: ''
})
const showShareFlag = ref(false)
const showShare = () => {
  showShareFlag.value = true
}

const toShowSave = () => {
  // 保存 弹框
  try {
    ElMessageBox.confirm("是否确认全部保存?", "信息", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
      // cancelButtonClass: "!bg-gray-500 !text-white",
      confirmButtonClass: "!bg-red-500 !border-none ",
      distinguishCancelAndClose: true,
    })
      .then(async () => {
        const data = await DocTranslateApi.saveDocx(curId);
        if (data) {
          message.success("提交成功");
        } else {
          message.error("提交失败");
        }
      })
      .catch(async (action: Action) => {
        console.log(action);
      });
  } catch {}
};

const auditForm = ref();
const toShowCheck = () => {
  // 审核确认 弹框
  auditForm.value.open(curId);
};

const openHistory = () => {
  toShowTable();
};
</script>

<style lang="scss" scoped>
.preview-box {
  width: 100%;
  min-height: 800px;
  color: #1d2129;
  background: #fff;
  border: 1px solid #f2f3f5;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgb(35 46 67 / 6%);
  padding: 30px;
  position: relative;

  .state {
    position: absolute;
    right: 200px;
    top: 15px;
    display: flex;
    align-items: center;
  }

  .showShare{
    position: absolute;
    right: 400px;
    top: 20px;
    color: #2988f4;
    font-size: 16px;
    cursor: pointer;
  }

  .showTable {
    position: absolute;
    right: 0;
    top: 0;
    color: #2988f4;
    font-size: 16px;
    text-align: right;
    padding: 20px 30px;
    cursor: pointer;

    &:hover {
      color: #083e7b;
    }
  }

  .showCheck,
  .showSave {
    position: absolute;
    right: 20px;
    bottom: -20px;
    color: #fff;
    font-size: 16px;
    text-align: right;
    cursor: pointer;
    font-weight: 500;
    background: #f30f12;
    border: 1px solid #f2494c;
    border-radius: 4px;
    height: 35px;
    line-height: 33px;
    padding: 0 20px;
    z-index: 2;

    &:hover {
      background: #cb1b1e;
    }
  }

  .showSave {
    background: #2988f4;
    border: 1px solid #2988f4;
    right: 120px;

    &:hover {
      background: #2163af;
    }
  }

  .space-item {
    width: 50%;
    padding: 0 20px 0 0;
    border-right: 1px solid #f2f3f5;

    &.space-item2 {
      border: none;
      padding: 0 0 0 10px;
    }
  }
}

.showTranslationText {
  position: relative;
}
</style>
