export default {
  app: {
    title: "恒瑞大模型平台",
  },
  common: {
    inputText: "请输入",
    selectText: "请选择",
    startTimeText: "开始时间",
    endTimeText: "结束时间",
    login: "登录",
    required: "该项为必填项",
    loginOut: "退出平台",
    document: "项目文档",
    profile: "个人中心",
    reminder: "温馨提示",
    loginOutMessage: "是否退出本平台？",
    back: "返回",
    ok: "确定",
    save: "保存",
    close: "关闭",
    reload: "重新加载",
    success: "成功",
    closeTab: "关闭标签页",
    closeTheLeftTab: "关闭左侧标签页",
    closeTheRightTab: "关闭右侧标签页",
    closeOther: "关闭其他标签页",
    closeAll: "关闭全部标签页",
    prevLabel: "上一步",
    nextLabel: "下一步",
    skipLabel: "跳过",
    doneLabel: "结束",
    menu: "菜单",
    menuDes: "以路由的结构渲染的菜单栏",
    collapse: "展开缩收",
    collapseDes: "展开和缩放菜单栏",
    tagsView: "标签页",
    tagsViewDes: "用于记录路由历史记录",
    tool: "工具",
    toolDes: "用于设置定制平台",
    query: "查询",
    shrink: "收起",
    expand: "展开",
    confirmTitle: "平台提示",
    exportMessage: "是否确认导出数据项？",
    importMessage: "是否确认导入数据项？",
    createSuccess: "新增成功",
    updateSuccess: "修改成功",
    delMessage: "是否删除所选中数据？",
    delDataMessage: "是否删除数据？",
    delNoData: "请选择需要删除的数据",
    delSuccess: "删除成功",
    index: "序号",
    status: "状态",
    createTime: "创建时间",
    updateTime: "更新时间",
    copy: "复制",
    copySuccess: "复制成功",
    copyError: "复制失败",
    tip: "提示",
    action: "操作",
    delete: "删除",
    deleteSuccess: "删除成功",
    deleteError: "删除失败",
    cancel: "取消",
    confirm: "确认",
    search: "搜索",
    add: "新增",
    addSuccess: "新增成功",
    addError: "新增失败",
    edit: "编辑",
    editSuccess: "编辑成功",
    editError: "编辑失败",
    export: "导出",
    exportSuccess: "导出成功",
    exportError: "导出失败",
    import: "导入",
    importSuccess: "导入成功",
    importError: "导入失败",
    generate: "生成",
    generateSuccess: "生成成功",
    generateError: "生成失败",
    logout: "退出",
    logoutSuccess: "退出成功",
    logoutError: "退出失败",
    test: "测试",
    testSuccess: "测试成功",
    testError: "测试失败",
    refresh: "刷新",
    refreshSuccess: "刷新成功",
    refreshError: "刷新失败",
    reset: "重置",
    resetSuccess: "重置成功",
    resetError: "重置失败",
    creator: "创建人",
    actions: "操作",
    startDate: "开始日期",
    endDate: "结束日期",
    noEmpty: "不可为空",
    submit: "提交",
    submitSuccess: "提交成功",
    idError: "未获取到ID",
  },
  lock: {
    lockScreen: "锁定屏幕",
    lock: "锁定",
    lockPassword: "锁屏密码",
    unlock: "点击解锁",
    backToLogin: "返回登录",
    entrySystem: "进入平台",
    placeholder: "请输入锁屏密码",
    message: "锁屏密码错误",
  },
  error: {
    noPermission: `抱歉，您无权访问此页面。`,
    pageError: "抱歉，您访问的页面不存在。",
    networkError: "抱歉，服务器报告错误。",
    returnToHome: "返回首页",
  },
  permission: {
    hasPermission: `请设置操作权限标签值`,
    hasRole: `请设置角色权限标签值`,
  },
  setting: {
    projectSetting: "项目配置",
    theme: "主题",
    layout: "布局",
    systemTheme: "平台主题",
    menuTheme: "菜单主题",
    interfaceDisplay: "界面显示",
    breadcrumb: "面包屑",
    breadcrumbIcon: "面包屑图标",
    collapseMenu: "折叠菜单",
    hamburgerIcon: "折叠图标",
    screenfullIcon: "全屏图标",
    sizeIcon: "尺寸图标",
    localeIcon: "多语言图标",
    messageIcon: "消息图标",
    tagsView: "标签页",
    logo: "标志",
    greyMode: "灰色模式",
    fixedHeader: "固定头部",
    headerTheme: "头部主题",
    cutMenu: "切割菜单",
    copy: "拷贝",
    clearAndReset: "清除缓存并且重置",
    copySuccess: "拷贝成功",
    copyFailed: "拷贝失败",
    footer: "页脚",
    uniqueOpened: "菜单手风琴",
    tagsViewIcon: "标签页图标",
    reExperienced: "请重新退出登录体验",
    fixedMenu: "固定菜单",
  },
  size: {
    default: "默认",
    large: "大",
    small: "小",
  },
  login: {
    welcome: "欢迎使用本平台",
    message: "开箱即用的中后台管理平台",
    tenantname: "租户名称",
    username: "用户名",
    password: "密码",
    code: "验证码",
    login: "登录",
    relogin: "重新登录",
    otherLogin: "其他登录方式",
    register: "注册",
    checkPassword: "确认密码",
    remember: "记住密码",
    hasUser: "已有账号？去登录",
    forgetPassword: "忘记密码?",
    tenantNamePlaceholder: "请输入租户名称",
    usernamePlaceholder: "请输入用户名",
    passwordPlaceholder: "请输入密码",
    codePlaceholder: "请输入验证码",
    mobileTitle: "手机登录",
    mobileNumber: "手机号码",
    mobileNumberPlaceholder: "请输入手机号码",
    backLogin: "返回",
    getSmsCode: "获取验证码",
    btnMobile: "手机登录",
    btnQRCode: "二维码登录",
    qrcode: "扫描二维码登录",
    btnRegister: "注册",
    SmsSendMsg: "验证码已发送",
  },
  captcha: {
    verification: "请完成安全验证",
    slide: "向右滑动完成验证",
    point: "请依次点击",
    success: "验证成功",
    fail: "验证失败",
  },
  router: {
    login: "登录",
    socialLogin: "社交登录",
    home: "首页",
    analysis: "分析页",
    workplace: "工作台",
  },
  analysis: {
    newUser: "新增用户",
    unreadInformation: "未读消息",
    transactionAmount: "成交金额",
    totalShopping: "购物总量",
    monthlySales: "每月销售额",
    userAccessSource: "用户访问来源",
    january: "一月",
    february: "二月",
    march: "三月",
    april: "四月",
    may: "五月",
    june: "六月",
    july: "七月",
    august: "八月",
    september: "九月",
    october: "十月",
    november: "十一月",
    december: "十二月",
    estimate: "预计",
    actual: "实际",
    directAccess: "直接访问",
    mailMarketing: "邮件营销",
    allianceAdvertising: "联盟广告",
    videoAdvertising: "视频广告",
    searchEngines: "搜索引擎",
    weeklyUserActivity: "每周用户活跃量",
    activeQuantity: "活跃量",
    monday: "周一",
    tuesday: "周二",
    wednesday: "周三",
    thursday: "周四",
    friday: "周五",
    saturday: "周六",
    sunday: "周日",
  },
  workplace: {
    welcome: "你好",
    happyDay: "祝你开心每一天!",
    toady: "今日晴",
    notice: "通知公告",
    project: "项目数",
    access: "项目访问",
    toDo: "待办",
    introduction: "一个正经的简介",
    shortcutOperation: "快捷入口",
    operation: "操作",
    index: "指数",
    personal: "个人",
    team: "团队",
    quote: "引用",
    contribution: "贡献",
    hot: "热度",
    yield: "产量",
    dynamic: "动态",
    push: "推送",
    follow: "关注",
  },
  form: {
    input: "输入框",
    inputNumber: "数字输入框",
    default: "默认",
    icon: "图标",
    mixed: "复合型",
    textarea: "多行文本",
    slot: "插槽",
    position: "位置",
    autocomplete: "自动补全",
    select: "选择器",
    selectGroup: "选项分组",
    selectV2: "虚拟列表选择器",
    cascader: "级联选择器",
    switch: "开关",
    rate: "评分",
    colorPicker: "颜色选择器",
    transfer: "穿梭框",
    render: "渲染器",
    radio: "单选框",
    button: "按钮",
    checkbox: "多选框",
    slider: "滑块",
    datePicker: "日期选择器",
    shortcuts: "快捷选项",
    today: "今天",
    yesterday: "昨天",
    aWeekAgo: "一周前",
    week: "周",
    year: "年",
    month: "月",
    dates: "日期",
    daterange: "日期范围",
    monthrange: "月份范围",
    dateTimePicker: "日期时间选择器",
    dateTimerange: "日期时间范围",
    timePicker: "时间选择器",
    timeSelect: "时间选择",
    inputPassword: "密码输入框",
    passwordStrength: "密码强度",
    operate: "操作",
    change: "更改",
    restore: "还原",
    disabled: "禁用",
    disablement: "解除禁用",
    delete: "删除",
    add: "添加",
    setValue: "设置值",
    resetValue: "重置值",
    set: "设置",
    subitem: "子项",
    formValidation: "表单验证",
    verifyReset: "验证重置",
    remark: "备注",
  },
  watermark: {
    watermark: "水印",
  },
  table: {
    table: "表格",
    index: "序号",
    title: "标题",
    author: "作者",
    createTime: "创建时间",
    action: "操作",
    pagination: "分页",
    reserveIndex: "叠加序号",
    restoreIndex: "还原序号",
    showSelections: "显示多选",
    hiddenSelections: "隐藏多选",
    showExpandedRows: "显示展开行",
    hiddenExpandedRows: "隐藏展开行",
    header: "头部",
  },
  action: {
    create: "新增",
    add: "新增",
    del: "删除",
    delete: "删除",
    edit: "编辑",
    update: "编辑",
    preview: "预览",
    more: "更多",
    sync: "同步",
    save: "保存",
    detail: "详情",
    export: "导出",
    import: "导入",
    generate: "生成",
    logout: "强制退出",
    test: "测试",
    typeCreate: "字典类型新增",
    typeUpdate: "字典类型编辑",
    dataCreate: "字典数据新增",
    dataUpdate: "字典数据编辑",
  },
  dialog: {
    dialog: "弹窗",
    open: "打开",
    close: "关闭",
  },
  sys: {
    api: {
      operationFailed: "操作失败",
      errorTip: "错误提示",
      errorMessage: "操作失败,平台异常!",
      timeoutMessage: "登录超时,请重新登录!",
      apiTimeoutMessage: "接口请求超时,请刷新页面重试!",
      apiRequestFailed: "请求出错，请稍候重试",
      networkException: "网络异常",
      networkExceptionMsg: "网络异常，请检查您的网络连接是否正常!",
      errMsg401: "用户没有权限（令牌、用户名、密码错误）!",
      errMsg403: "用户得到授权，但是访问是被禁止的。!",
      errMsg404: "网络请求错误,未找到该资源!",
      errMsg405: "网络请求错误,请求方法未允许!",
      errMsg408: "网络请求超时!",
      errMsg500: "服务器错误,请联系管理员!",
      errMsg501: "网络未实现!",
      errMsg502: "网络错误!",
      errMsg503: "服务不可用，服务器暂时过载或维护!",
      errMsg504: "网络超时!",
      errMsg505: "http版本不支持该请求!",
      errMsg901: "演示模式，无法进行写操作!",
    },
    app: {
      logoutTip: "温馨提醒",
      logoutMessage: "是否确认退出平台?",
      menuLoading: "菜单加载中...",
    },
    exception: {
      backLogin: "返回登录",
      backHome: "返回首页",
      subTitle403: "抱歉，您无权访问此页面。",
      subTitle404: "抱歉，您访问的页面不存在。",
      subTitle500: "抱歉，服务器报告错误。",
      noDataTitle: "当前页无数据",
      networkErrorTitle: "网络错误",
      networkErrorSubTitle: "抱歉，您的网络连接已断开，请检查您的网络！",
    },
    lock: {
      unlock: "点击解锁",
      alert: "锁屏密码错误",
      backToLogin: "返回登录",
      entry: "进入平台",
      placeholder: "请输入锁屏密码或者用户密码",
    },
    login: {
      backSignIn: "返回",
      signInFormTitle: "登录",
      ssoFormTitle: "三方授权",
      mobileSignInFormTitle: "手机登录",
      qrSignInFormTitle: "二维码登录",
      signUpFormTitle: "注册",
      forgetFormTitle: "重置密码",
      signInTitle: "开箱即用的中后台管理平台",
      signInDesc: "输入您的个人详细信息开始使用！",
      policy: "我同意xxx隐私政策",
      scanSign: `扫码后点击"确认"，即可完成登录`,
      loginButton: "登录",
      registerButton: "注册",
      rememberMe: "记住我",
      forgetPassword: "忘记密码?",
      otherSignIn: "其他登录方式",
      // notify
      loginSuccessTitle: "登录成功",
      loginSuccessDesc: "欢迎回来",
      // placeholder
      accountPlaceholder: "请输入账号",
      passwordPlaceholder: "请输入密码",
      smsPlaceholder: "请输入验证码",
      mobilePlaceholder: "请输入手机号码",
      policyPlaceholder: "勾选后才能注册",
      diffPwd: "两次输入密码不一致",
      userName: "账号",
      password: "密码",
      confirmPassword: "确认密码",
      email: "邮箱",
      smsCode: "短信验证码",
      mobile: "手机号码",
    },
  },
  profile: {
    user: {
      title: "个人信息",
      username: "用户名称",
      nickname: "用户昵称",
      mobile: "手机号码",
      email: "用户邮箱",
      dept: "所属部门",
      posts: "所属岗位",
      roles: "所属角色",
      sex: "性别",
      man: "男",
      woman: "女",
      createTime: "创建日期",
    },
    info: {
      title: "基本信息",
      basicInfo: "基本资料",
      resetPwd: "修改密码",
      userSocial: "社交信息",
    },
    rules: {
      nickname: "请输入用户昵称",
      mail: "请输入邮箱地址",
      truemail: "请输入正确的邮箱地址",
      phone: "请输入正确的手机号码",
      truephone: "请输入正确的手机号码",
    },
    password: {
      oldPassword: "旧密码",
      newPassword: "新密码",
      confirmPassword: "确认密码",
      oldPwdMsg: "请输入旧密码",
      newPwdMsg: "请输入新密码",
      cfPwdMsg: "请输入确认密码",
      pwdRules: "长度在 6 到 20 个字符",
      diffPwd: "两次输入密码不一致",
    },
  },
  cropper: {
    selectImage: "选择图片",
    uploadSuccess: "上传成功",
    modalTitle: "头像上传",
    okText: "确认并上传",
    btn_reset: "重置",
    btn_rotate_left: "逆时针旋转",
    btn_rotate_right: "顺时针旋转",
    btn_scale_x: "水平翻转",
    btn_scale_y: "垂直翻转",
    btn_zoom_in: "放大",
    btn_zoom_out: "缩小",
    preview: "预览",
  },
  "OAuth 2.0": "OAuth 2.0", // 避免菜单名是 OAuth 2.0 时，一直 warn 报错
  translate: {
    file: {
      share: "分享",
      shareUser: "分享人",
      acceptShareUser: '被分享人',
      expireTime: "过期时间",
      docPermission: "文档权限",
      shareBack: "撤销分享",
      preview: "预览",
      shareTime: "分享时间",
      expireTimeTip: "授权已过期",
      fileName: "文件名称",
      fileNamePlaceholder: "请输入文件名称",
      translateType: "翻译类型",
      selectType: "选择类型",
      translateStatus: "翻译状态",
      selectStatus: "选择状态",
      createTime: "创建时间",
      startDate: "开始日期",
      endDate: "结束日期",
      search: "搜索",
      reset: "重置",
      uploadDoc: "上传文档",
      fileSize: "文件大小",
      creator: "创建人",
      translateTime: "翻译时间",
      actions: "操作",
      view: "查看",
      edit: "编辑",
      download: "下载",
      delete: "删除",
      translating: "翻译进行中...",
      translateError: "翻译异常",
      downloadSuccess: "下载成功!",
      dragUploadText: "将文件拖到此处，或",
      clickUpload: "点击上传",
      supportFileTypes:
        "支持Word、Excel、PPT、PDF、TXT、RTF 文档类型，多个文档上传，且单个文档不超过 {maxFileSize} MB",
      selectScene: "选择场景",
      uploadAndTranslate: "上 传 并 翻 译",
      cancel: "取 消",
      unsupportedFileType: "请上传平台支持的类型文件!",
      fileSizeExceeded: "超出上传文件限制 {maxFileSize} MB!",
      selectTranslateType: "请选择翻译类型",
      uploadFileFirst: "请上传文件",
      aiTranslating: "AI正在加速翻译中...",
      uploadSuccess: "上传成功",
      uploadFailed: "上传失败，请您重新上传！",
      maxFileLimit: "最多只能上传10个文件！",
    },
    text: {
      translateScene: "翻译场景",
      selectTranslateScene: "选择翻译场景",
      enableCache: "开启缓存",
      inputTextToTranslate: "请输入要翻译的文字",
      translation: "译文",
      copy: "复制",
      chineseSimplified: "中文（简体）",
      english: "英语",
      translating: "翻译中...",
      copySuccess: "复制成功",
      translateHistory: "翻译历史记录",
      deleteTip: "删除后无法恢复，是否继续？",
    },
    record: {
      number: "序号",
      translateScene: "翻译场景",
      original: "原文",
      translate: "译文",
      deleteTip: "删除后无法恢复，是否继续？",
      source: "翻译来源",
      type: "翻译类型",
      type1: "中译英",
      type2: "英译中",
      exportFile: "翻译历史列表",
      translateHistoryRecord: "翻译历史记录",
      creator: "创建人",
      createTime: "创建时间",
    },
    ocr: {
      title: "OCR图片识别",
      uploadImage: "上传图片：",
      recognitionResult: "识别结果：",
      uploadLimit: "单次限制1张，图片不超过50MB, 格式为JPG、PNG",
      pleaseUpload: "请上传图片",
      uploadFormatError: "上传图片只能是 JPG/PNG 格式!",
      uploadSizeError: "上传图片大小不能超过 50MB!",
      recognitionFailed: "识别失败，请重试",
    },
    statistics: {
      timeRange: "时间范围",
      startDate: "开始日期",
      endDate: "结束日期",
      search: "搜索",
      reset: "重置",
      uploadUsers: "上传用户量",
      totalTranslations: "文档翻译总量",
      completedTranslations: "翻译完成数量",
      pendingSubmissions: "待提交数量",
      underReview: "审核中数量",
      approved: "已通过数量",
      rejected: "未通过数量",
      quantity: "数量",
      uploadDocumentCount: "上传文档数量",
    },
    history: {
      problemReport: "问题上报",
      searchOriginal: "搜索原文",
      enterOriginal: "请输入原文",
      searchTranslation: "搜索译文",
      enterModifiedTranslation: "请输入修改后译文",
      search: "搜索",
      reset: "重置",
      serialNumber: "序号",
      original: "原文",
      translation: "译文",
      modifiedOriginal: "修改的原文",
      modifiedTranslation: "修改的译文",
      remark: "备注",
      creator: "创建人",
      auditor: "审核人",
      auditStatus: "审核状态",
      createTime: "创建时间",
      actions: "操作",
      edit: "编辑",
      delete: "删除",
      deleteSuccess: "删除成功",
      confirmAudit: "是否确认审核通过?",
      auditInfo: "审核信息",
      approve: "通过",
      reject: "不通过",
      auditApproved: "审核已通过",
      auditRejected: "审核未通过",
    },
    report: {
      documentName: "文档名称",
      enterDocumentName: "请输入文档名称",
      auditor: "审核人员",
      enterAuditorName: "请输入审核人员姓名",
      searchOriginal: "搜索原文",
      enterOriginal: "请输入原文",
      searchTranslation: "搜索译文",
      enterModifiedTranslation: "请输入修改后译文",
      auditStatus: "审核状态",
      selectAuditStatus: "请选择审核状态",
      createTime: "创建时间",
      startDate: "开始日期",
      endDate: "结束日期",
      search: "搜索",
      reset: "重置",
      export: "导出",
      number: "序号",
      original: "原文",
      translation: "译文",
      remark: "备注",
      creator: "创建人",
      actions: "操作",
      audit: "审核",
      view: "查看",
      delete: "删除",
      deleteSuccess: "删除成功",
      confirmAudit: "是否确认审核通过?",
      auditInfo: "审核信息",
      approve: "通过",
      reject: "不通过",
      auditApproved: "审核已通过",
      auditRejected: "审核未通过",
      problemReportFile: "问题上报.xls",
    },
    version: {
      fileName: "文件名称",
      fileNamePlaceholder: "请输入文件名称",
      aiLearning: "是否AI学习",
      selectAiLearning: "请选择是否AI学习",
      fileSource: "文件来源",
      selectFileSource: "请选择文件来源",
      createTime: "创建时间",
      startDate: "开始日期",
      endDate: "结束日期",
      yes: "是",
      no: "否",
      documentTranslation: "文档翻译",
      manualUpload: "人工上传",
      search: "搜索",
      reset: "重置",
      add: "新增",
      originalLink: "原文链接",
      translationLink: "译文链接",
      currentVersion: "当前版本",
      actions: "操作",
      preview: "预览",
      edit: "编辑",
      historyVersion: "历史版本",
      delete: "删除",
      exportFileName: "翻译版本信息.xls",
    },
    editor: {
      modifyTranslation: "修改翻译",
      historyRecords: "历史修改记录",
      unsupportedFileType: "暂不支持该文件类型",
    },
    sentence: {
      sentenceComparison: "逐句对照",
      historyRecords: "历史修改记录",
    },
    textPreview: {
      paragraphEdit: "逐段修改",
      share: "分享",
      shareUser: "分享用户",
      selectUser: "请选择用户",
      sharePermission: "分享权限",
      viewOnly: "仅查看",
      viewAndDownload: "查看并下载",
      editable: "可编辑",
      editAndDownload: "编辑并下载",
      confirm: "确定",
      historyRecords: "历史修改记录",
      auditStatus: "审核状态",
      audit: "审核",
    },
    reportEdit: {
      problemReport: "问题上报",
      auditor: "审核人:",
      original: "原文:",
      translation: "译文:",
      remark: "备注",
      enterContent: "请输入内容",
      close: "关 闭",
    },
    versionForm: {
      fileName: "文件名称",
      fileNamePlaceholder: "上传原文自动带入文件名称",
      currentVersion: "当前版本",
      enterCurrentVersion: "请输入当前版本",
      uploadOriginal: "上传原文",
      uploadTranslation: "上传译文",
      confirm: "确 定",
      cancel: "取 消",
    },
    versionHistory: {
      fileSource: "文件来源",
      selectFileSource: "请选择文件来源",
      documentTranslation: "文档翻译",
      manualUpload: "人工上传",
      createTime: "创建时间",
      startDate: "开始日期",
      endDate: "结束日期",
      search: "搜索",
      reset: "重置",
      add: "新增",
      fileName: "文件名称",
      originalLink: "原文链接",
      translationLink: "译文链接",
      currentVersion: "当前版本",
      actions: "操作",
      preview: "预览",
      edit: "编辑",
      delete: "删除",
      exportFileName: "翻译历史版本信息.xls",
    },
    uploadFile: {
      delete: "删除",
      uploading: "上传中，请勿操作或关闭页面",
      dragText: "将文件拖到此处，或",
      clickUpload: "点击上传",
      supportTypes: "支持Word、Excel、PPT、PDF、TXT、RTF 文档类型，单个文档不超过 {maxFileSize} MB",
      unsupportedFileType: "请上传平台支持的类型文件!",
      fileSizeExceeded: "超出上传文件限制 {maxFileSize} MB!",
      uploadSuccess: "上传成功",
      uploadFailed: "上传失败，请您重新上传！",
      maxOneFile: "最多只能上传1个文件！",
    },
    versionHistoryForm: {
      uploadOriginal: "上传原文",
      uploadTranslation: "上传译文",
      confirm: "确 定",
      cancel: "取 消",
    },
  },
  wordPreview: {
    keywordDisplay: "关键词显示",
    grammarIssueDisplay: "语法问题显示",
    templateFormatIssueDisplay: "模板格式问题显示",
    problemReportRecords: "问题上报记录",
    userRevisionRecord: "用户：{nickName} 的修订记录",
    paragraphEdit: "逐段修改",
    formatEdit: "格式修改",
    leftRightLayout: "左右排版",
    topBottomLayout: "上下排版",
    sentenceComparison: "逐句对照",
    previousPage: "上一页",
    nextPage: "下一页",
    zoomIn: "放大",
    zoomOut: "缩小",
    resetZoom: "重置缩放",
    closeComparison: "关闭对照",
    openComparison: "打开对照",
    closeRevisionRecord: "关闭修订记录",
    openRevisionRecord: "打开修订记录",
    downloadOriginal: "下载原文",
    downloadTranslation: "下载译文",
    uploadVersion: "上传版本",
    retranslate: "重新翻译",
    original: "原文",
    oldTranslation: "旧译文",
    newTranslation: "新译文",
    copyNewTranslation: "复制新译文",
    confirmReplaceTranslation: "确认使用 新译文 替换 旧译文",
    cancel: "取消",
    getUserInfoFailed: "获取用户信息失败：",
    copySuccess: "复制成功",
    problemReport: "问题上报",
    textAdd: "新增文字",
    textRemove: "删除文字",
    paragraphAdd: "新增段落",
    paragraphRemove: "删除段落",
    textStyleModify: "修改文字样式",
    paragraphStyleModify: "修改段落样式",
    unknown: "未知",
    noTranslationSelected: "没有选中译文",
    newTranslationEmpty: "新译文为空",
    translationSuffix: "（译文）",
    previewDocument: "预览文档",
    reviwer: "审阅模式",
  },
  menu: {
    // 文档分类质控
    TMF分类质控: "TMF分类质控",
    统计分析: "统计分析",
    文档上传: "文档上传",
    任务详情: "任务详情",
    我的文档: "我的文档",
    驳回文档: "驳回文档",

    // AI翻译
    AI翻译: "AI翻译",
    文本翻译: "文本翻译",
    翻译历史: "翻译历史",
    文档翻译: "文档翻译",
    整篇预览: "整篇预览",
    逐段对照: "逐段对照",
    逐句对照: "逐句对照",
    问题上报: "问题上报",
    问题列表: "问题列表",
    定稿上传: "定稿上传",
    定稿历史版本: "定稿历史版本",

    // ocr
    OCR图片识别: "OCR图片识别",

    // 对象存储
    对象存储: "对象存储",
    存储配置: "存储配置",
    对象列表: "对象列表",

    // 系统日志
    系统日志: "系统日志",
    操作日志: "操作日志",
    登录日志: "登录日志",

    // 系统管理
    系统管理: "系统管理",
    菜单管理: "菜单管理",
    用户管理: "用户管理",
    角色管理: "角色管理",
    字典管理: "字典管理",
    部门管理: "部门管理",
    岗位管理: "岗位管理",
    配置管理: "配置管理",
    代码生成: "代码生成",
    定时任务: "定时任务",

    // 版本管理
    版本管理: "版本管理",
  },
  home: {
    mainTitle: "AI翻译+医学大模型结合",
    subTitle: "AI智能翻译引擎，为您节约宝贵的时间",
    textTranslation: "文本翻译",
    documentTranslation: "文档翻译",
  },
  popVersion: {
    title: "版本更新通知",
    versionNumber: "版本号",
    updateTime: "更新时间",
    updateContent: "更新内容",
    cancel: "取消",
    confirmRead: "确认已读",
    markReadFailed: "标记已读失败:",
  },
  docFile: {
    fileName: "文件名称",
    fileNamePlaceholder: "请输入文件名称",
    aiFileType: "AI文件类别",
    aiFileTypePlaceholder: "请输入AI文件类别",
    aiAnalysis: "AI分析",
    statusPlaceholder: "请选择状态",
    createTime: "创建时间",
    startDate: "开始日期",
    endDate: "结束日期",
    fileSize: "文件大小",
    aiClassificationResult: "AI分类结果",
    aiQualityResult: "AI质检结果",
    reason: "原因",
    aiAnalysisCount: "AI分析次数",
    approvalStatus: "审批状态",
    approved: "已通过",
  },
  docAudit: {
    fileName: "文件名称",
    fileNamePlaceholder: "请输入文件名称",
    aiClassificationResult: "AI分类结果",
    aiClassificationPlaceholder: "请输入AI分类结果",
    aiQualityResult: "AI质检结果",
    statusPlaceholder: "请选择状态",
    aiAnalysis: "AI分析",
    errorReason: "异常原因",
    errorReasonPlaceholder: "请选择异常原因",
    createTime: "创建时间",
    startDate: "开始日期",
    endDate: "结束日期",
    batchDelete: "批量删除",
    serialNumber: "序号",
    fileSize: "文件大小",
    reason: "原因",
    aiAnalysisCount: "AI分析次数",
    approvalStatus: "审批状态",
    pendingApproval: "待审批",
    analysisTime: "分析时间",
    approve: "审批",
    selectAnalysisRecords: "请选择要分析的记录",
    aiAnalysisConfirm: "所选文档会利用人工智能技术进行自动分类，质检，是否继续？",
    submitSuccess: "提交成功，正在处理中，请稍后刷新列表",
    selectDeleteRecords: "请选择要删除的记录",
    batchDeleteConfirm: "所选文档将会被删除并无法找回，是否继续？",
    deleteSuccess: "提交成功，正在删除中，请稍后刷新列表",
    exportFileName: "任务审批列表.xls",
  },
  auditDetail: {
    fileApproval: "文件审批",
    fileAttributes: "文件属性",
    fileName: "文件名称",
    folderLocation: "所在文件夹",
    aiClassificationTip: "AI分类提示",
    approvalProcess: "审批流程",
    demoApprovalProcess: "演示审批流程",
    namingRules: "命名规则",
    chineseNamingPrinciples: "《中国药品通用名称命名原则》",
    fileVersion: "文件版本",
    schemeNumber: "方案编号",
    projectName: "项目名称",
    demoProject: "演示项目",
    countryName: "国家名称",
    china: "中国",
    filePlan: "文件计划",
    move: "移动",
    approve: "审批",
    approvalOpinion: "审批意见",
    approvalResult: "审批结果",
    aiQualityTip: "AI质检提示",
    qualityResult: "质检结果",
    approvalPassed: "审核通过",
    approvalRejected: "审核不通过",
    reason: "原因",
    selectRejectReason: "请选择驳回原因",
    rejectReason: "驳回原因",
    responsible: "责任人",
  },
  docExit: {
    fileName: "文件名称",
    fileNamePlaceholder: "请输入文件名称",
    aiFileType: "AI文件类别",
    aiFileTypePlaceholder: "请输入AI文件类别",
    aiAnalysis: "AI分析",
    statusPlaceholder: "请选择状态",
    createTime: "创建时间",
    startDate: "开始日期",
    endDate: "结束日期",
    fileSize: "文件大小",
    aiClassificationResult: "AI分类结果",
    aiQualityResult: "AI质检结果",
    reason: "原因",
    aiAnalysisCount: "AI分析次数",
    approvalStatus: "审批状态",
    rejected: "已驳回",
  },
  docStatistics: {
    total: "分析总数",
    analysis: "分析中数量",
    success: "分析成功数量",
    error: "分析异常数量",
    pass: "质检通过数量",
    fail: "质检不通过数量",
    uncertain: "质检不确定数量",
    uploadDocumentCount: "上传文档数量",
    aiFileType: "AI文件类别",
    aiFileTypePlaceholder: "请输入AI文件类别",
    createTime: "创建时间",
    startDate: "开始日期",
    endDate: "结束日期",
    category: "类别",
    fileCount: "文件数量",
    aiAnalysisCount: "ai分析次数",
    qualityPassCount: "质检通过数量",
  },
  docFileForm: {
    uploadFile: "上传文件",
    dragOrClickUpload: "将文件拖到此处，或 点击上传",
    pdfOnlyTip: "提示：仅允许导入 .pdf格式文件！",
    pleaseUploadFile: "请上传文件",
    aiAnalyzing: "AI正在加速分析中...",
    uploadFailed: "上传失败，请您重新上传！",
    maxFilesExceeded: "最多只能上传1000个文件！",
  },
};
