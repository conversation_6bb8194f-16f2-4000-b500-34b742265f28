<template>
  <div class="ocr-box">
    <h2>{{ t('translate.ocr.title') }}</h2>
    <div class="orc-container">
      <div class="upload-pic">
        <p class="title">{{ t('translate.ocr.uploadImage') }}</p>
        <el-upload class="avatar-uploader" list-type="picture-card" accept=".jpg,.jpeg,.png"
          :before-upload="beforeUpload" :limit="1" :auto-upload="false" :on-preview="handlePictureCardPreview"
          @change="handleChange">
          <Icon icon="ep:plus" />
        </el-upload>
        <p class="tips">{{ t('translate.ocr.uploadLimit') }}</p>
      </div>
      <div class="ocr-result">
        <p class="title">{{ t('translate.ocr.recognitionResult') }}</p>
        <div class="result-content" v-if="resultText">{{ resultText }}</div>
        <el-button class="copy-btn" type="primary" v-if="resultText" @click="handleCopy" :icon="CopyDocument">{{
          t("translate.text.copy") }}</el-button>
      </div>
    </div>
    <el-dialog v-model="dialogVisible">
      <img style="max-width: 100%; max-height: 100%;" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="TranslateText">
import { ref } from "vue";
import { CopyDocument } from '@element-plus/icons-vue'
import { useMessage } from "@/hooks/web/useMessage";
import { ocrTranslate } from "@/api/system/ocr";
import { copyTextToClipboard } from '@/utils/index';
import { useI18n } from "vue-i18n";
defineOptions({ name: "TranslateOcr" });

const { t } = useI18n();
const message = useMessage();
const resultText = ref("");
const loading = ref(false);
const formData = ref({
  language: "zh",
  file: null as any,
});

const dialogVisible = ref(false);
const dialogImageUrl = ref('');

const toTranslate = async () => {
  if (!formData.value.file) {
    message.error(t('translate.ocr.pleaseUpload'));
    return;
  }
  loading.value = true;
  try {
    const res = await ocrTranslate(formData.value);
    resultText.value = res.data;
  } catch (error: any) {
    message.error(error.message || t('translate.ocr.recognitionFailed'));
  } finally {
    loading.value = false;
  }
};

// 添加change事件处理函数
const handleChange = (file: any) => {
  formData.value.file = file.raw;
  toTranslate();
};

const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogVisible.value = true
}


const beforeUpload = (file: File) => {
  const isJPG = ["image/jpeg", "image/png"].includes(file.type);
  if (!isJPG) {
    message.error(t('translate.ocr.uploadFormatError'));
    return false;
  }
  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error(t('translate.ocr.uploadSizeError'));
    return false;
  }

  return true;
};

const handleCopy = () => {
  copyTextToClipboard(resultText.value);
  message.success(t('translate.text.copySuccess'));
};

</script>

<style lang="scss" scoped>
.ocr-box {
  width: 100%;
  min-height: 800px;
  padding: 20px;
  background: #fff;

  h2 {
    text-align: center;
    line-height: 60px;
  }

  .orc-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;

    &>div {
      flex: 1;
      border: 1px dashed #eee;
      border-radius: 6px;
      padding: 10px 30px;

      .title {
        line-height: 40px;
        font-size: 18px;
        font-weight: 700;
        margin-bottom: 20px;
      }
    }
  }

  .ocr-upload {}

  .tips {
    margin-top: 20px;
    font-size: 12px;
    color: #666;
  }

  .ocr-result {
    .result-content {
      margin-bottom: 20px;
      padding: 10px;
      border: 1px solid #eee;
      border-radius: 6px;
    }
  }
}
</style>
