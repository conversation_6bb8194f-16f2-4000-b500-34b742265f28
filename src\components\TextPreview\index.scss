.choose-box {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #f1f1f1;
  border-radius: 8px;
  font-size: 16px;

  .choose-title {
    margin-bottom: 10px;

    &.marginTop {
      margin-top: 20px;
    }
  }

  ::v-deep(.el-checkbox-group) {

    .greenText {

      .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #0ac958;
      }
    }

    .redText {

      .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #e70f12;
      }
    }

    .blueText {

      .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #2b6de7;
      }
    }

    .el-checkbox__label {
      display: flex;
      align-items: center;
    }
  }

  .points {
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: middle;
    border-radius: 50%;
    margin-right: 2px;

    &.greenBg {
      background: #4ed464;
    }

    &.redBg {
      background: #df4d4f;
    }

    &.blueBg {
      background: #3875e7;
    }
  }
}

.text-preview {
  height: 100%;
  position: relative;
}

::v-deep(.text-preview-wrap) {
  height: calc(100vh - 410px);
  display: flex;
  border-radius: 8px;
  // overflow: auto;
  background-color: #e9e9e9;

  &.toggleWrap {
    flex-direction: column;
    overflow: hidden;

    .text-preview-left,
    .text-preview-right {
      border-radius: 8px;
      height: 50%;
      padding: 20px;

      .text-content {
        height: 100%;
        overflow: auto;
      }
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 700;
    margin: 10px 0 20px 0;
    text-align: center;
    font-size: var(--titleSize);
  }

  p {
    line-height: 1.5;
    margin-bottom: 20px;
    font-size: var(--size);
  }
}

.text-preview-left,
.text-preview-right {
  flex: 1;
  position: relative;
  overflow: auto;

  /** 
  * 滚动条样式
  */
  &::-webkit-scrollbar {
    width: 4px;
  }

  /* 整个滚动条的样式 */
  &::-webkit-scrollbar {
    width: 10px;
    /* 滚动条宽度 */
  }

  /* 滚动轨道的样式 */
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, .05);
    /* 轨道背景颜色 */
  }

  /* 滚动滑块的样式 */
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, .3);
    border-radius: 5px;
    /* 滑块背景颜色 */
  }

  /* 当鼠标悬停在滑块上时改变颜色 */
  &::-webkit-scrollbar-thumb:hover {
    background: #555;
    /* 鼠标悬停时的滑块颜色 */
  }
}

.text-preview-left {
  padding: 20px 10px 20px 20px;
}

.text-preview-right {
  padding: 20px 20px 20px 10px;
}

.text-content {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
}

.page-tool {
  height: 35px;
  padding: 0 10px;
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  // width: 700px;
  // display: inline-block;
  // display: flex;
  // align-items: center;
  // justify-content: center;
  border-radius: 8px;
  -webkit-user-select: none;
  user-select: none;
  z-index: 2;
  background: rgba(66, 66, 66, 0.9);
  color: white;
  cursor: pointer;
  white-space: nowrap;

  &.toggleWrap {
    position: fixed;
    right: 10px;
    top: 50%;
    left: auto;
    z-index: 11;
    flex-direction: column;
    padding: 0;
    width: 90px;
    height: fit-content;
    background: rgba(66, 66, 66, 1);
    transform: translateY(-50%);

    .page-tool-item {
      width: 100%;
      text-align: center;
    }
  }
}

.page-tool-item {
  cursor: pointer;
  padding: 0 10px;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
  display: inline-block;

  &.active,
  &:hover,
  &:focus,
  &:active {
    background-color: rgba(255, 255, 255, 0.1);


  }
}

::v-deep(.dashed-underline) {
  text-decoration: 2px underline dashed rgb(240, 166, 76);
  cursor: pointer;

  &:hover {
    text-decoration-color: rgb(234, 70, 25);
  }
}