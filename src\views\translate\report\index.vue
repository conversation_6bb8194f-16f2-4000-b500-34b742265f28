<template>
  <ContentWrap>
    <el-form class="-mb-15px" :model="queryParams" ref="historyQueryFormRef" :inline="true" label-width="100px">
      <el-form-item :label="t('translate.report.documentName')" prop="fileName">
        <el-input v-model="queryParams.fileName" :placeholder="t('translate.report.enterDocumentName')" clearable @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.report.auditor')" prop="auditName">
        <el-input v-model="queryParams.auditName" :placeholder="t('translate.report.enterAuditorName')" clearable @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.report.searchOriginal')" prop="origin">
        <el-input v-model="queryParams.origin" :placeholder="t('translate.report.enterOriginal')" clearable @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.report.searchTranslation')" prop="translation">
        <el-input v-model="queryParams.translation" :placeholder="t('translate.report.enterModifiedTranslation')" clearable @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.report.auditStatus')" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" :placeholder="t('translate.report.selectAuditStatus')" clearable class="!w-240px">
          <el-option v-for="item in getDictOptions(DICT_TYPE.REPORT_AUDIT_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('translate.report.createTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :start-placeholder="t('translate.report.startDate')"
          :end-placeholder="t('translate.report.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-row>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" />{{ t('translate.report.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" />{{ t('translate.report.reset') }}</el-button>
          <el-button type="success" plain @click="handleExport" :loading="exportLoading" v-hasPermi="['system:change:export']"> <Icon icon="ep:download" class="mr-5px" /> {{ t('translate.report.export') }} </el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('translate.report.number')" align="center" width="120px">
        <template #default="scope">
          {{ (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.report.documentName')" align="left" prop="fileName" />
      <el-table-column :label="t('translate.report.original')" align="left" prop="origin" />
      <el-table-column :label="t('translate.report.translation')" align="left" prop="translation" />
      <!-- <el-table-column :label="t('translate.report.modifiedOriginal')" align="center" prop="originChange"
          ><template #default="scope">
            {{ scope.row.originChange || "-" }}
          </template>
        </el-table-column> -->
      <!-- <el-table-column :label="t('translate.report.modifiedTranslation')" align="center" prop="translationChange">
          <template #default="scope">
            {{ scope.row.translationChange || "-" }}
          </template>
        </el-table-column> -->
      <el-table-column :label="t('translate.report.remark')" align="center" prop="remark" />
      <el-table-column :label="t('translate.report.creator')" align="center" prop="createName" />
      <el-table-column :label="t('translate.report.auditor')" align="center" prop="auditName" />
      <el-table-column :label="t('translate.report.auditStatus')" align="center" prop="auditStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.REPORT_AUDIT_STATUS" :value="scope.row.auditStatus || '0'" />
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.report.createTime')" align="center" width="170">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.report.actions')" align="center" width="180px">
        <template #default="scope">
          <el-button link type="danger" @click="handleCheck(scope.row.id)" v-hasPermi="['system:change:audit']"> {{ t('translate.report.audit') }} </el-button>
          <el-button link type="primary" @click="handleEdit(scope.row.id)" v-hasPermi="['system:change:query']"> {{ t('translate.report.view') }} </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['system:change:delete']"> {{ t('translate.report.delete') }} </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>
  <report-edit-form ref="reportEditForm" />
</template>
<script lang="ts" name="AuditForm" setup>
import { DocChangeApi } from "@/api/system/change";
import { ElMessageBox } from "element-plus";
import type { Action } from "element-plus";
import { formatDate } from "@/utils/formatTime";
import { useEmit } from "@/hooks/web/useEmitt";
import { DICT_TYPE, getDictOptions } from "@/utils/dict";
import ReportEditForm from "@/views/translate/report/reportEditForm.vue";
import download from "@/utils/download";
import { useI18n } from 'vue-i18n';

defineOptions({ name: "TranslateReport" });

const { t } = useI18n();
const router = useRouter();
const message = useMessage(); // 消息弹窗
const loading = ref(true); // 列表的加载中
const list = ref<any[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const queryParams = reactive({
  transId: "",
  pageNo: 1,
  pageSize: 10,
  fileName: "",
  auditName: "",
  auditStatus: "",
  origin: "",
  translation: "",
  createTime: [],
});
const historyQueryFormRef = ref();
const reportEditForm = ref();
const exportLoading = ref(false); // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await DocChangeApi.getDocChangePage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  historyQueryFormRef.value?.resetFields();
  handleQuery();
};

onMounted(() => {
  getList();
});

// 打开详情编辑
const handleEdit = (id: number) => {
  reportEditForm.value.open(id);
};

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    await DocChangeApi.deleteDocChange(id);
    message.success(t('translate.report.deleteSuccess'));
    // 刷新列表
    await getList();
  } catch {}
};

/** 确认审核按钮操作 */
const handleCheck = async (id: number) => {
  try {
    // 审核二次确认
    ElMessageBox.confirm(t('translate.report.confirmAudit'), t('translate.report.auditInfo'), {
      confirmButtonText: t('translate.report.approve'),
      cancelButtonText: t('translate.report.reject'),
      type: "warning",
      cancelButtonClass: "!bg-red-500 !text-white",
      confirmButtonClass: "!bg-green-500",
      distinguishCancelAndClose: true,
    })
      .then(async () => {
        await DocChangeApi.auditDocChange({ id, auditStatus: 1 });
        ElMessage({
          type: "success",
          message: t('translate.report.auditApproved'),
        });
        getList();
      })
      .catch(async (action: Action) => {
        if (action === "cancel") {
          await DocChangeApi.auditDocChange({ id, auditStatus: 2 });
          ElMessage({
            type: "info",
            message: t('translate.report.auditRejected'),
          });
          getList();
        }
      });
  } catch {}
};

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm();
    // 发起导出
    exportLoading.value = true;
    const data = await DocChangeApi.exportDocChange(queryParams);
    download.excel(data, t('translate.report.problemReportFile'));
  } catch {
  } finally {
    exportLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
.question-table {
  overflow: hidden;
}
::v-deep(.el-popper) {
  max-width: 300px !important;
}
</style>
