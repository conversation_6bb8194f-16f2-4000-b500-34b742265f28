<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
      <el-form-item :label="t('translate.statistics.timeRange')" prop="createTime">
        <el-date-picker
          v-model="timeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :start-placeholder="t('translate.statistics.startDate')"
          :end-placeholder="t('translate.statistics.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          {{ t("translate.statistics.search") }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          {{ t("translate.statistics.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <div class="header_data">
      <div class="data_item" style="background: #409eff; color: #fff">
        <p>{{ t("translate.statistics.uploadUsers") }}</p>
        <p>{{ transData.userNum }}</p>
      </div>
      <div class="data_item" style="background: #f940ff; color: #fff">
        <p>{{ t("translate.statistics.totalTranslations") }}</p>
        <p>{{ transData.total }}</p>
      </div>
      <div class="data_item" style="background: #67c23a; color: #fff">
        <p>{{ t("translate.statistics.completedTranslations") }}</p>
        <p>{{ transData.success }}</p>
      </div>
      <div class="data_item" style="background: #909399; color: #fff">
        <p>{{ t("translate.statistics.pendingSubmissions") }}</p>
        <p>{{ transData.submit }}</p>
      </div>
      <div class="data_item" style="background: #e6a23c; color: #fff">
        <p>{{ t("translate.statistics.underReview") }}</p>
        <p>{{ transData.waiting }}</p>
      </div>
      <div class="data_item" style="background: #67c23a; color: #fff">
        <p>{{ t("translate.statistics.approved") }}</p>
        <p>{{ transData.pass }}</p>
      </div>
      <div class="data_item" style="background: #f56c6c; color: #fff">
        <p>{{ t("translate.statistics.rejected") }}</p>
        <p>{{ transData.fail }}</p>
      </div>
    </div>
    <Echart :options="chartOptions" :height="420" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Echart } from "@/components/Echart";
import { ContentWrap } from "@/components/ContentWrap";
import { StatisticsApi, StatisticsVO } from "@/api/system/statistics";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const timeRange = ref<[string, string] | undefined>(undefined);
/** 文档信息 列表 */
defineOptions({ name: "TransStatistic" });
const transData = ref({});
const loading = ref(true); // 列表的加载中
const queryParams = reactive<StatisticsVO>({
  type: "",
  startTime: "",
  endTime: "",
});

interface ChartData {
  ymd: string;
  num: number;
}

interface ChartOptions {
  grid: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  tooltip: {
    trigger: "axis";
  };
  xAxis: {
    type: "category";
    data: string[];
    axisLabel: {
      rotate: number;
    };
  };
  yAxis: {
    type: "value";
    name: string;
  };
  series: Array<{
    type: "line";
    data: number[];
    name: string;
    smooth: boolean;
  }>;
}

// 获取最近7天的日期
const getLastSevenDays = (): string[] => {
  const dates: string[] = [];
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    dates.push(date.toISOString().split("T")[0]);
  }
  return dates;
};
// 获取指定日期范围内的所有日期
const getDateRange = (startDate: string, endDate: string): string[] => {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  while (start <= end) {
    dates.push(start.toISOString().split("T")[0]);
    start.setDate(start.getDate() + 1);
  }

  return dates;
};

const chartOptions = ref<ChartOptions>({
  grid: {
    top: 60,
    right: 20,
    bottom: 60,
    left: 60,
  },
  tooltip: {
    trigger: "axis",
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      rotate: 45,
    },
  },
  yAxis: {
    type: "value",
    name: computed(() => t("translate.statistics.quantity")),
  },
  series: [
    {
      type: "line",
      data: [],
      name: computed(() => t("translate.statistics.uploadDocumentCount")),
      smooth: true,
    },
  ],
});

// 模拟API数据
const mockData = ref<ChartData[]>([]);
// 处理数据
const processData = () => {
  let dates: string[];
  if (timeRange.value && timeRange.value.length === 2) {
    // 使用选择的时间范围
    dates = getDateRange(timeRange.value[0].split(" ")[0], timeRange.value[1].split(" ")[0]);
  } else {
    // 默认使用最近7天
    dates = getLastSevenDays();
  }
  const dataMap = new Map(mockData.value.map((item) => [item.ymd, item.num]));
  // 为日期范围创建数据，没有数据的日期补0
  const chartData = dates.map((ymd) => ({
    ymd,
    num: dataMap.get(ymd) || 0,
  }));
  // 更新图表数据
  chartOptions.value = {
    ...chartOptions.value,
    xAxis: {
      ...chartOptions.value.xAxis,
      data: chartData.map((item) => item.ymd),
    },
    series: [
      {
        ...chartOptions.value.series[0],
        data: chartData.map((item) => item.num),
      },
    ],
  };
};
const queryFormRef = ref(); // 搜索的表单
/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    queryParams.startTime = timeRange.value ? timeRange.value[0] : "";
    queryParams.endTime = timeRange.value ? timeRange.value[1] : "";
    transData.value = await StatisticsApi.getTransData(queryParams);
    mockData.value = await StatisticsApi.getTransDaysNum(queryParams);
    processData();
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.startTime = "";
  queryParams.endTime = "";
  timeRange.value = undefined;
  handleQuery();
};
/** 初始化 **/
onMounted(() => {
  getList();
});
</script>
<style scoped lang="scss">
.header_data {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;

  .data_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    padding: 20px 0;
    gap: 5px;
    width: 60%;
    margin: 0 auto;

    p:nth-child(2) {
      font-size: 20px;
      font-weight: 700;
    }
  }
}
</style>
