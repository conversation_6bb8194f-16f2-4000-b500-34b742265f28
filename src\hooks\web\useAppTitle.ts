/**
 * 应用标题管理 Hook
 */
import { computed, watch } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useAppStore } from '@/store/modules/app'
import { i18n } from '@/plugins/vueI18n'

/**
 * 应用标题管理 Hook
 * @returns 应用标题相关的方法和响应式数据
 */
export const useAppTitle = () => {
  const { t } = useI18n()
  const appStore = useAppStore()

  /**
   * 获取国际化的应用标题
   */
  const appTitle = computed(() => {
    try {
      return t('app.title')
    } catch {
      // 如果国际化未初始化，返回环境变量中的默认标题
      return import.meta.env.VITE_APP_TITLE || '恒瑞大模型平台'
    }
  })

  /**
   * 更新页面标题
   * @param pageTitle 页面标题（可选）
   */
  const updatePageTitle = (pageTitle?: string) => {
    const title = pageTitle ? `${appTitle.value} - ${pageTitle}` : appTitle.value
    
    if (document) {
      document.title = title
    }
  }

  /**
   * 设置应用标题（用于动态修改）
   * @param title 新的应用标题
   */
  const setAppTitle = (title: string) => {
    appStore.setTitle(title)
    updatePageTitle()
  }

  // 监听语言变化，自动更新页面标题
  if (i18n && i18n.global) {
    watch(
      () => i18n.global.locale.value,
      () => {
        updatePageTitle()
      },
      { immediate: true }
    )
  }

  return {
    appTitle,
    updatePageTitle,
    setAppTitle
  }
}
