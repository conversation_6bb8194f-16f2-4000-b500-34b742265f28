import request from '@/config/axios'

// 维保机构 VO
export interface OrgServiceVO {
  id: number // id
  name: string // 机构名称
  address: string // 机构地址
  codeNumber: number // 机构代码证
  contactUser: string // 法定代表人
  contactPhoneNumber: number // 联系方式
  employeeNumber: number // 员工总数
  deviceNumber: number // 设备总数
}

// 维保机构 API
export const OrgServiceApi = {
  // 查询维保机构分页
  getOrgServicePage: async (params: any) => {
    return await request.get({ url: `/system/org-service/page`, params })
  },

  // 查询维保机构详情
  getOrgService: async (id: number) => {
    return await request.get({ url: `/system/org-service/get?id=` + id })
  },

  // 新增维保机构
  createOrgService: async (data: OrgServiceVO) => {
    return await request.post({ url: `/system/org-service/create`, data })
  },

  // 修改维保机构
  updateOrgService: async (data: OrgServiceVO) => {
    return await request.put({ url: `/system/org-service/update`, data })
  },

  // 删除维保机构
  deleteOrgService: async (id: number) => {
    return await request.delete({ url: `/system/org-service/delete?id=` + id })
  },

  // 导出维保机构 Excel
  exportOrgService: async (params) => {
    return await request.download({ url: `/system/org-service/export-excel`, params })
  },
}
