<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="120px">
      <el-form-item :label="t('docAudit.fileName')" prop="fileName">
        <el-input v-model="queryParams.fileName" :placeholder="t('docAudit.fileNamePlaceholder')" clearable @keyup.enter="handleQuery" class="!w-220px" />
      </el-form-item>
      <el-form-item :label="t('docAudit.aiClassificationResult')" prop="type">
        <el-input v-model="queryParams.type" :placeholder="t('docAudit.aiClassificationPlaceholder')" clearable @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('docAudit.aiQualityResult')" prop="type">
        <el-select v-model="queryParams.result" :placeholder="t('docAudit.statusPlaceholder')" clearable class="!w-220px">
          <el-option value="Pass">Pass</el-option>
          <el-option value="Fail">Fail</el-option>
          <el-option value="Uncertain">Uncertain</el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('docAudit.aiAnalysis')" prop="aiStatus">
        <el-select v-model="queryParams.aiStatus" :placeholder="t('docAudit.statusPlaceholder')" clearable class="!w-220px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.AI_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('docAudit.errorReason')" prop="errorCode">
        <el-select v-model="queryParams.errorCode" :placeholder="t('docAudit.errorReasonPlaceholder')" clearable class="!w-220px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ERROR_CODE)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('docAudit.createTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          :start-placeholder="t('docAudit.startDate')"
          :end-placeholder="t('docAudit.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          {{ t("common.query") }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          {{ t("common.reset") }}
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['system:doc:create']">
          <Icon icon="ep:plus" class="mr-5px" />
          {{ t("common.add") }}
        </el-button>
        <el-button type="success" @click="toShowAnalysis">{{ t("docAudit.aiAnalysis") }}</el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading" v-hasPermi="['system:doc:export']">
          <Icon icon="ep:download" class="mr-5px" />
          {{ t("common.export") }}
        </el-button>
        <el-button type="danger" @click="batchDelete">{{ t("docAudit.batchDelete") }}</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" ref="multipleTableRef" :data="list" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column :label="t('docAudit.serialNumber')" align="center" width="90px">
        <template #default="scope">
          {{ (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="t('docAudit.fileName')" align="center" prop="fileName" />
      <!--      <el-table-column :label="t('docAudit.fileUrl')" align="center" prop="fileUrl" />-->
      <el-table-column :label="t('docAudit.fileSize')" align="center" prop="fileSize" :formatter="fileSizeFormatter" />
      <el-table-column :label="t('docAudit.aiClassificationResult')" align="center" prop="type" />
      <el-table-column :label="t('docAudit.aiQualityResult')" align="center" prop="result" />
      <el-table-column :label="t('docAudit.reason')" align="center" prop="reason" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-if="scope.row.reason != '' && scope.row.reason != null">
            <div v-for="(res, index) in scope.row.reason.split(',')" :key="index">
              <dict-tag :type="DICT_TYPE.REJECT_REASON" :value="res" />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('docAudit.aiAnalysisCount')" align="center" prop="aiNum" />
      <el-table-column :label="t('docAudit.aiAnalysis')" align="center" prop="aiStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.AI_STATUS" :value="scope.row.aiStatus" />
        </template>
      </el-table-column>
      <el-table-column :label="t('docAudit.errorReason')" align="center" prop="errorCode">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ERROR_CODE" :value="scope.row.errorCode" />
        </template>
      </el-table-column>
      <el-table-column :label="t('docAudit.approvalStatus')" align="center" prop="status">
        <template #default="scope">
          <el-tag type="primary" v-if="scope.row.isAi">{{ t("docAudit.pendingApproval") }}</el-tag>
          <el-tag type="warning" v-else>{{ t("docAudit.pendingApproval") }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('docAudit.createTime')" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column :label="t('docAudit.analysisTime')" align="center" prop="updateTime" :formatter="dateFormatter" width="180px">
        <template #default="scope">
          {{ scope.row.aiStatus == 1 ? "" : formatDate(scope.row.updateTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('common.action')" align="center" width="150px">
        <template #default="scope">
          <el-button link type="primary" @click="handleAudit(scope.row.id)" v-hasPermi="['system:doc:audit']">
            {{ t("docAudit.approve") }}
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['system:doc:delete']">
            {{ t("common.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      class="float-right mt-20px mb-20px"
      background
      layout="total, sizes, prev, pager, next, jumper"
      v-model:current-page="queryParams.pageNo"
      v-model:page-size="queryParams.pageSize"
      :page-sizes="[10, 20, 50, 100, 200, 300, 400, 500, 1000]"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DocFileForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { ElMessageBox, ElTable } from "element-plus";
import { dateFormatter, formatDate } from "@/utils/formatTime";
import { DocFileApi, DocFileVO } from "@/api/system/doc";
import DocFileForm from "@/views/system/doc/audit/DocFileForm.vue";
import { fileSizeFormatter } from "@/utils";
import { DICT_TYPE, getIntDictOptions } from "@/utils/dict";
import download from "@/utils/download";

const router = useRouter();

/** 文档信息 列表 */
defineOptions({ name: "DocFile" });

const message = useMessage(); // 消息弹窗
const { t } = useI18n(); // 国际化

const loading = ref(true); // 列表的加载中
const list = ref<DocFileVO[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fileName: undefined,
  type: undefined,
  status: 0,
  aiStatus: undefined,
  result: undefined,
  errorCode: undefined,
  createTime: [],
});
const queryFormRef = ref(); // 搜索的表单
const exportLoading = ref(false); // 导出的加载中

const multipleTableRef = ref<InstanceType<typeof ElTable>>();
const multipleSelection = ref<User[]>([]);
const handleSelectionChange = (val: User[]) => {
  multipleSelection.value = val;
};
const clearSelection = () => {
  multipleTableRef.value?.clearSelection();
};
const toShowAnalysis = () => {
  const ids = multipleSelection.value.map((s) => s.id);
  if (ids.length === 0) {
    message.warning(t("docAudit.selectAnalysisRecords"));
    return;
  }
  ElMessageBox.confirm(t("docAudit.aiAnalysisConfirm"), t("common.tip"), {
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
  })
    .then(async () => {
      await DocFileApi.batchAiAnalysis(ids);
      clearSelection();
      message.success(t("docAudit.submitSuccess"));
      getList();
    })
    .catch((e) => console.error(e));
};
const batchDelete = () => {
  const ids = multipleSelection.value.map((s) => s.id);
  if (ids.length === 0) {
    message.warning(t("docAudit.selectDeleteRecords"));
    return;
  }
  ElMessageBox.confirm(t("docAudit.batchDeleteConfirm"), t("common.tip"), {
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
  })
    .then(async () => {
      await DocFileApi.batchDelete(ids);
      clearSelection();
      message.success(t("docAudit.deleteSuccess"));
      getList();
    })
    .catch((e) => console.error(e));
};

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await DocFileApi.getDocFilePage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

const handleSizeChange = (val: number) => {
  queryParams.pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val;
  getList();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields();
  queryParams.status = 0;
  handleQuery();
};

/** 添加/修改操作 */
const formRef = ref();
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id);
};

/** 添加/修改操作 */
// const auditRef = ref()
const handleAudit = (id: number) => {
  // auditRef.value.open(id)
  router.push({
    path: "auditDetail",
    query: {
      id,
    },
  });
};

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    await DocFileApi.deleteDocFile(id);
    message.success(t("common.delSuccess"));
    // 刷新列表
    await getList();
  } catch {}
};

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm();
    // 发起导出
    exportLoading.value = true;
    const data = await DocFileApi.exportDocFile(queryParams);
    download.excel(data, t("docAudit.exportFileName"));
  } catch {
  } finally {
    exportLoading.value = false;
  }
};

let intervalFn = null;
/** 初始化 **/
onMounted(() => {
  getList();
  intervalFn = setInterval(() => {
    getList();
  }, 10000);
});
</script>
