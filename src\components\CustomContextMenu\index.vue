<template>
  <div v-if="show" :style="{ top: position.y + 'px', left: position.x + 'px' }" class="context-menu">
    <button class="btn" v-for="item in menuList" :key="item.type" @click="onClick(item.type)">{{ item.menuName }}</button>
    <!-- 其他按钮或菜单项 -->
    <button class="btn close" @click="onClose">关闭</button>
  </div>
</template>

<script setup>

const props = defineProps({
  show: <PERSON><PERSON><PERSON>,
  position: Object,
  menuList: Array,
});

const emit = defineEmits(["close", "handleClick"]);

const onClick = (type) => {
  // 处理点击事件
  emit("handleClick", type);
};

const onClose = () => {
  emit("close");
};
</script>

<style>
.context-menu {
  position: fixed;
  background-color: #fff;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  border-radius: 6px;
  overflow: hidden;

  .btn {
    display: block;
    width: 90px;
    height: 30px;
    line-height: 30px;
    outline: none;
    border: 0;
    background: none;
    color: #666;
    font-size: 14px;
    border-bottom: 1px solid #f1f2f3;
    padding: 0;
    margin: 0;
    cursor: pointer;

    &:last-child {
      border-bottom: 0;
    }
    &:hover {
      color: #159be8;
      background: #f2f3f4;
    }
  }
}
</style>
