<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
      <el-form-item :label="t('translate.file.fileName')" prop="fileName">
        <el-input v-model="queryParams.fileName" :placeholder="t('translate.file.fileNamePlaceholder')" clearable
          @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.file.translateType')" prop="type">
        <el-select v-model="queryParams.type" :placeholder="t('translate.file.selectType')" class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.TRANSFORM_TYPE)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="t('translate.file.translateStatus')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="t('translate.file.selectStatus')" class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRANSLATE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item :label="t('translate.file.shareTime')" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          :start-placeholder="t('translate.file.startDate')" :end-placeholder="t('translate.file.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          {{ t("common.search") }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          {{ t("common.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('translate.file.fileName')" align="left" prop="fileName" />
      <!-- <el-table-column :label="t('translate.file.fileUrl')" align="center" prop="fileUrlOrigin" /> -->
      <el-table-column :label="t('translate.file.fileSize')" align="center" prop="fileSize"
        :formatter="fileSizeFormatter" />
      <el-table-column :label="t('translate.file.translateType')" align="center" prop="type" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRANSFORM_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.file.translateStatus')" align="center" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRANSLATE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <!-- <el-table-column :label="t('translate.file.auditStatus')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.AUDIT_STATUS" :value="scope.row.auditStatus" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="t('translate.file.reason')" align="center" prop="reason" /> -->
      <el-table-column :label="t('translate.file.shareUser')" align="center" prop="createName" />
      <el-table-column :label="t('translate.file.acceptShareUser')" align="center" prop="userName" />
      <el-table-column :label="t('translate.file.docPermission')" align="center" prop="permission" />
      <el-table-column :label="t('translate.file.createTime')" align="center" prop="createTime"
        :formatter="dateFormatter" width="180px" />
      <el-table-column :label="t('translate.file.expireTime')" align="center" prop="expireTime"
        :formatter="dateFormatter" width="180px" />
      <el-table-column :label="t('translate.file.actions')" align="center" min-width="120px">
        <template #default="scope">
          <el-button v-if="scope.row.permission === 'view'" link type="success" @click="previewFile(scope.row)">
            {{ t("translate.file.view") }}
          </el-button>
          <el-button v-if="scope.row.permission === 'edit'" link type="primary" @click="toEdit(scope.row)">
            {{ t('translate.file.edit') }}
          </el-button>
          <el-button v-if="isShowDelete(scope.row.creator)" link type="danger" @click="handleDelete(scope.row.id)">
            {{ t("translate.file.shareBack") }}
          </el-button>
          <el-button v-else v-hasPermi="['system:translate:delete']" link type="danger"
            @click="handleDelete(scope.row.id)">
            {{ t("translate.file.shareBack") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <WordPreviewByUrl ref="previewWordRef" />
</template>

<script setup lang="ts">
import { dateFormatter, formatDate } from "@/utils/formatTime";
import { ShareApi, ShareVO } from "@/api/system/share";
import { fileSizeFormatter } from "@/utils";
import { DICT_TYPE, getIntDictOptions } from "@/utils/dict";
import { getUserProfile } from "@/api/system/user/profile";
import WordPreviewByUrl from '@/components/WordPreview/index_preview.vue'

/** 文档分享 */
defineOptions({ name: "Share" });
const router = useRouter();
const message = useMessage(); // 消息弹窗
const { t } = useI18n(); // 国际化

const userInfo = ref([]);

const isShowDelete = (creator: string) => {
  return creator === userInfo.value.id + '';
}

const loading = ref(true); // 列表的加载中
const list = ref<DocTranslateVO[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fileName: undefined,
  type: undefined,
  status: undefined,
  isAi: undefined,
  createTime: [],
});
const queryFormRef = ref(); // 搜索的表单
const exportLoading = ref(false); // 导出的加载中

// 允许预览的文件类型
const allowedFileTypes = getIntDictOptions(DICT_TYPE.ALLOW_VIEW_FILE_TYPE);

const getFileType = (fileUrl: string) => {
  return fileUrl.split("/")?.pop()?.split(".")[1];
};

const allowedViwer = computed(() => {
  return (url) => {
    if (!url) return false;
    const fileType = getFileType(url);
    return allowedFileTypes.some((item) => item.label === fileType);
  };
});

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await ShareApi.getSharePage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields();
  handleQuery();
};

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    await ShareApi.deleteShare(id);
    message.success(t("common.delSuccess"));
    // 刷新列表
    await getList();
  } catch { }
};

/** 初始化 **/
onMounted(async () => {
  userInfo.value = await getUserProfile();
  getList();
});

const toEdit = (row: any) => {
  let now = new Date();
  let expireTime = new Date(row.expireTime);
  if (now > expireTime) {
    message.warning(t("translate.file.expireTimeTip"));
    return;
  }
  router.push({
    path: "/translate/preview",
    query: { id: row.transId },
  });
};

// 预览
const previewWordRef = ref()
const previewFile = async (row: any) => {
  let now = new Date();
  let expireTime = new Date(row.expireTime);
  if (now > expireTime) {
    message.warning(t("translate.file.expireTimeTip"));
    return;
  }
  previewWordRef.value.open(row);
}
</script>

<style lang="scss" scoped></style>
