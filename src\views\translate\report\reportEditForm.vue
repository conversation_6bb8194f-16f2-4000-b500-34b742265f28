<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :width="800" :close-on-click-modal="false" destory-on-close>
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="80px">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.reportEdit.auditor')" prop="auditName">
            <el-input v-model="formData.auditName" readonly />
            <!-- <choose-user @set-user-id="setUserId" /> -->
            <!-- <choose-dept-user @set-user-id="setUserId" /> -->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.reportEdit.original')" prop="origin">
            <el-input v-model="formData.origin" :placeholder="t('translate.reportEdit.enterContent')" type="textarea"
              autosize readonly />
            <!-- <p class="origin-text">{{ formData.origin }}</p> -->
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-button class="retranslate" type="primary" size="mini" @click="handleRetranslate" :loading="retranslateLoading">
              <Icon icon="ep:refresh" class="mr-5px" />
              重新翻译
            </el-button>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.reportEdit.translation')" prop="translation">
            <el-input v-model="formData.translation" :placeholder="t('translate.reportEdit.enterContent')"
              type="textarea" autosize readonly />
            <!-- <p class="origin-text">{{ formData.translation }}</p> -->
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row v-if="whichPannel === 'left'">
        <el-col :span="24">
          <el-form-item label="原文修改" prop="originChange">
            <el-input v-model="formData.originChange" placeholder="请输入内容" type="textarea" autosize />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="whichPannel === 'right'">
        <el-col :span="24">
          <el-form-item label="译文修改" prop="translationChange">
            <el-input v-model="formData.translationChange" placeholder="请输入内容" type="textarea" autosize />
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.reportEdit.remark')">
            <el-input v-model="formData.remark" :placeholder="t('translate.reportEdit.enterContent')" type="textarea"
              rows="5" readonly />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <!-- <el-button :disabled="formLoading" type="primary" @click="submitForm">提 交</el-button> -->
      <el-button @click="dialogVisible = false">{{ t('translate.reportEdit.close') }}</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { FormRules } from "element-plus";
import { DocChangeApi } from "@/api/system/change";
import { useEmit } from "@/hooks/web/useEmitt";
import ChooseUser from "@/components/ChooseUser/ChooseUser.vue";
import ChooseDeptUser from "@/components/ChooseUser/ChooseDeptUser.vue";
import { useI18n } from 'vue-i18n';
// import { DocTranslateApi } from "@/api/system/translate";

defineOptions({ name: "ReportEditForm" });

const props = defineProps({
  whichPannel: {
    type: String,
    default: "right",
  },
});

const { t } = useI18n();
const message = useMessage(); // 消息弹窗

const dialogVisible = ref(false); // 弹窗的是否展示
const dialogTitle = ref(computed(() => t('translate.reportEdit.problemReport'))); // 弹窗的标题
const formLoading = ref(true);
const formData = ref({
  transId: "",
  origin: "",
  translation: "",
  auditName: "",
  remark: "",
});

const setUserId = (userId: string) => {
  formData.value.auditName = userId;
};

const resetForm = () => {
  formData.value = {
    transId: "",
    origin: "",
    translation: "",
    auditName: "",
    remark: "",
  };
};

const formRules = reactive<FormRules>({
  // auditName: [
  //   {
  //     required: true,
  //     message: "原文内容不能为空",
  //     trigger: "blur",
  //   },
  // ],
  // origin: [
  //   {
  //     required: true,
  //     message: "原文内容不能为空",
  //     trigger: "blur",
  //   },
  // ],
  // translation: [
  //   {
  //     required: true,
  //     message: "译文内容不能为空",
  //     trigger: "blur",
  //   },
  // ],
});
const formRef = ref(); // 表单 Ref
const router = useRouter();
// const curId = computed(() => router.currentRoute.value.query?.id);
/** 打开弹窗 */
const open = async (id: number) => {
  resetForm();
  dialogVisible.value = true;
  formLoading.value = true;
  const data = await DocChangeApi.getDocChange(id);
  formData.value = data;
  formLoading.value = false;
};

const close = () => {
  dialogVisible.value = false;
};

defineExpose({ open, close }); // 提供 open 方法，用于打开弹窗

// // 重新翻译
// const retranslateFormData = ref({
//   from: "zh",
//   to: "en",
//   content: "",
//   isCache: true,
// });
// const retranslateLoading = ref(false);
// const handleRetranslate = async () => {
//   if (!formData.value.origin) return;
//   retranslateLoading.value = true;
//   retranslateFormData.value.content = formData.value.origin;
//   await DocTranslateApi.textTranslate(retranslateFormData.value).then((res) => {
//     formData.value.translation = res?.content || '';
//   });
//   retranslateLoading.value = false;
// };

/** 提交表单 */
const emit = defineEmits(["success"]); // 定义 success 事件，用于操作成功后的回调
// const submitForm = async () => {
//   // 校验表单
//   if (!formRef) return;
//   const valid = await formRef.value.validate();
//   if (!valid) return;
//   // 提交请求
//   formLoading.value = true;
//   try {
//     const data: any = formData.value;
//     if (data.id) {
//       await DocChangeApi.updateDocChange(data);
//     } else {
//       await DocChangeApi.createDocChange(data);
//     }
//     message.success("提交成功!");
//     dialogVisible.value = false;
//     // 发送操作成功的事件
//     // emit("success", props.whichPannel === "left" ? data.originChange : data.translationChange);
//     emit("success");
//   } finally {
//     formLoading.value = false;
//   }
// };
</script>
<style lang="scss" scoped>
.origin-text {
  color: #999;
  line-height: 20px;
  font-size: 14px;
  border: 1px solid #eee;
  padding: 5px 11px;
  border-radius: 4px;
}
</style>
