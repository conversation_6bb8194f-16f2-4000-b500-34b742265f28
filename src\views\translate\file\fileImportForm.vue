<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :width="1000">
    <el-upload
      class="layout-upload"
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="uploadUrl"
      :auto-upload="false"
      :data="queryParams"
      :disabled="formLoading"
      :headers="uploadHeaders"
      :limit="10"
      multiple
      :http-request="httpRequest"
      :on-error="submitFormError"
      :on-exceed="handleExceed"
      :on-change="handleChange"
      :on-success="submitFormSuccess"
      :accept="limitFileType.join(',')"
      drag
    >
      <div class="img-box">
        <img class="img" src="../../../assets/icons/add.png" alt="add" />
      </div>
      <div class="el-upload__text">
        {{ t("translate.file.dragUploadText") }}<em>{{ t("translate.file.clickUpload") }}</em>
      </div>
      <div class="el-upload__tip">
        <div class="img-line">
          <img class="img" src="../../../assets/icons/word.png" alt="word" />
          <p class="text">Word</p>
        </div>
        <div class="img-line">
          <img class="img" src="../../../assets/icons/pdf.png" alt="pdf" />
          <p class="text">PDF</p>
        </div>
        <div class="img-line">
          <img class="img" src="../../../assets/icons/excel.png" alt="excel" />
          <p class="text">Excel</p>
        </div>
        <div class="img-line">
          <img class="img" src="../../../assets/icons/ppt.png" alt="ppt" />
          <p class="text">PPT</p>
        </div>
        <div class="img-line">
          <img class="img" src="../../../assets/icons/rtf.png" alt="rtf" />
          <p class="text">RTF</p>
        </div>
        <div class="img-line">
          <img class="img" src="../../../assets/icons/txt.png" alt="txt" />
          <p class="text">TXT</p>
        </div>
      </div>
      <div class="upload__tip">{{ t("translate.file.supportFileTypes", { maxFileSize }) }}</div>
      <el-form class="queryForm" :model="queryParams" ref="queryFormRef" label-width="120px">
        <el-form-item :label="t('translate.file.selectScene')" prop="scene" required>
          <el-select v-model="queryParams.scene" :placeholder="t('translate.file.selectScene')" class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.SCENARIO)" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('translate.file.translateType')" prop="type" required>
          <el-select v-model="queryParams.type" :placeholder="t('translate.file.selectType')" class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.TRANSFORM_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-upload>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitFileForm">{{ t("translate.file.uploadAndTranslate") }}</el-button>
      <el-button @click="close">{{ t("translate.file.cancel") }}</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from "@/utils/dict";
import { UploadProps } from "element-plus";
import { useUpload3 } from "@/components/UploadFile/src/useUpload";
import { ElLoading } from "element-plus";
import { useI18n } from "vue-i18n";

const { uploadUrl, httpRequest } = useUpload3();
const { t } = useI18n();
const dialogVisible = ref(false); // 弹窗的是否展示
const dialogTitle = ref(computed(() => t("translate.file.uploadDoc"))); // 弹窗的标题

defineOptions({ name: "FileImportForm" });

const message = useMessage(); // 消息弹窗
const formLoading = ref(false); // 表单的加载中
const uploadRef = ref();
const queryParams = ref({ path: "", type: undefined, scene: undefined });
const uploadHeaders = ref(); // 上传 Header 头
const fileList = ref([]); // 文件列表
const updateSupport = ref(0); // 是否更新已经存在的用户数据
const colors = [
  { color: "#f56c6c", percentage: 20 },
  { color: "#e6a23c", percentage: 40 },
  { color: "#5cb87a", percentage: 60 },
  { color: "#1989fa", percentage: 80 },
  { color: "#6f7ad3", percentage: 100 },
];

// 监听 fileList 变化 - 手动切换文件列表是否展示

watch(fileList, () => {
  const uploadList = document.getElementsByClassName("el-upload-list");
  if (uploadList && uploadList[0]) {
    const uploadDom = uploadList[0];
    if (fileList.value.length > 0 || formLoading.value) {
      uploadDom.className = "el-upload-list el-upload-list--text show";
    } else {
      uploadDom.className = "el-upload-list el-upload-list--text";
    }
  }
});

const limitFileType = ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "txt", "rtf"];
const maxFileSize = 100;

const beforeFileUpload: UploadProps["beforeUpload"] = (rawFile) => {
  if (!limitFileType.some((fileType) => rawFile.name.endsWith(fileType))) {
    message.warning(t("translate.file.unsupportedFileType"));
    return false;
  }

  if (rawFile.size > 1024 * 1024 * maxFileSize) {
    message.warning(t("translate.file.fileSizeExceeded", { maxFileSize }));
    return false;
  }
  return true;
};
const handleChange: UploadProps["onChange"] = (file) => {
  file.status === "ready" && !beforeFileUpload(file.raw) && resetForm();
  queryParams.value.path = file.name;
};

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true;
  resetForm();
};

const close = () => {
  resetForm();
  dialogVisible.value = false;
};

defineExpose({ open }); // 提供 open 方法，用于打开弹窗

const loadingInstance = ref();
/** 提交表单 */
const submitFileForm = async () => {
  if (queryParams.value.type === undefined) {
    message.error(t("translate.file.selectTranslateType"));
    return;
  }

  if (fileList.value.length == 0) {
    message.error(t("translate.file.uploadFileFirst"));
    return;
  }
  loadingInstance.value = ElLoading.service({
    fullscreen: true,
    text: t("translate.file.aiTranslating"),
    background: "rgba(122, 122, 122, 0.8)",
  });
  unref(uploadRef)?.submit();
};

/** 文件上传成功 */
const emits = defineEmits(["success"]);
const submitFormSuccess = (response: any) => {
  close();
  loadingInstance.value.close();
  message.success(t("translate.file.uploadSuccess"));
  emits("success");
  return;
};

/** 上传错误提示 */
const submitFormError = (): void => {
  message.error(t("translate.file.uploadFailed"));
  formLoading.value = false;
};

/** 重置表单 */
const resetForm = async (): Promise<void> => {
  updateSupport.value = 0;
  fileList.value = [];
  formLoading.value = false;
  unref(uploadRef)?.clearFiles();
};

/** 文件数超出提示 */
const handleExceed = (data): void => {
  message.error(t("translate.file.maxFileLimit"));
};
</script>
<style lang="scss" scoped>
.layout-upload {
  .upload__tip {
    margin-top: 20px;
    color: #999;
  }
  .cur-file-box {
    height: 100%;

    .el-upload__tip {
      .img-line {
        width: 120px;
        padding: 10px 0;
        margin: 0 10px;

        .img {
          display: block;
          margin: 0 auto;
          width: 64px;
        }

        .text {
          font-size: 12px;
          color: #999;
          margin-top: 6px;
        }
      }
    }

    .file-name {
      text-align: center;
      font-weight: 700;
      font-size: 20px;
    }

    .btn-box {
      text-align: center;
    }
  }

  .cur-file-uploading {
    height: 100%;
    text-align: center;

    .btn-box {
      margin: 30px 0;
    }

    .percent {
      font-size: 20px;
      color: #999;
      width: 150px;
      height: 150px;
      margin: 30px auto;
      text-align: center;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;

      .el-progress {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .text {
      font-size: 20px;
      color: #333;
      margin-top: 50px;
      font-weight: 700;
    }
  }

  .img-box {
    text-align: center;

    .img {
      vertical-align: middle;
      height: 70px;
    }
  }

  .el-upload__text {
    margin: 20px 0 40px 0;
  }

  .el-upload__tip {
    display: flex;
    justify-content: center;
    color: #999;

    .img-line {
      width: 60px;
      text-align: center;
      border: 1px dashed #f1f1f1;
      border-radius: 4px;
      margin: 0 20px;

      .img {
        display: block;
        margin: 0 auto;
        width: 32px;
      }

      .text {
        font-size: 12px;
        color: #999;
        margin-top: 6px;
      }
    }
  }

  .queryForm {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px 0 0 0;
  }
}
</style>
