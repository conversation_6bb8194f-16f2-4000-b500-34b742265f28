<template>
  <div class="toggle-show" @click="toggleIcon = !toggleIcon">
    <Icon v-if="toggleIcon" color="#2988f4" :size="30" icon="ep:open" />
    <Icon v-else :size="30" icon="ep:turn-off" />
  </div>
  <div class="top-check-box" v-if="toggleIcon">
    <el-checkbox-group v-model="checkList" @change="changeCheckList">
      <el-checkbox class="greenText" :value="0"><span class="points greenBg"></span>{{ t("wordPreview.keywordDisplay")
      }}</el-checkbox>
      <!-- <el-checkbox class="redText" :value="1"><span class="points redBg"></span>{{ t('wordPreview.grammarIssueDisplay') }} </el-checkbox> -->
      <!-- <el-checkbox class="blueText" :value="2"><span class="points blueBg"></span>{{ t('wordPreview.templateFormatIssueDisplay') }} </el-checkbox> -->
    </el-checkbox-group>
    <el-checkbox-group v-model="modeList" @change="changeMode">
      <el-checkbox class="blueText" :value="1"><span class="points blueBg"></span>{{ t('wordPreview.reviwer') }}
      </el-checkbox>
    </el-checkbox-group>
    <p class="showTable" @click="toShowTable">{{ t("wordPreview.problemReportRecords") }} &gt;&gt;</p>
  </div>
  <div class="word-preview" v-loading="state.loading">
    <div class="word-flex-box" :class="toggleWrap && 'toggleWrap'" v-if="!state.loading">
      <div class="word-flex-wrap">
        <!-- <div class="popout-btn" @click="popout('left')">弹出到新窗口</div> -->
        <div class="word-wrap" :style="{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: `translate(-50%,-50%) scale(${state.scale})`,
          width: `${pageWidth}`,
          height: `${pageHeight}`,
        }">
          <DocumentEditor id="source" ref="wordPreviewSingleLeft" :documentServerUrl="VITE_ONLY_OFFICE_BASE_URL"
            :config="sourceConfig" :events_onDocumentReady="onDocumentReadySource" />

          <!-- <word-preview-single
            ref="wordPreviewSingleLeft"
            officeIdName="wordPreviewSingleLeft"
            :updateTime="props.updateTime"
            :currentPage="state.pageNum"
            :fileName="props.fileName"
            :fileUrl="state.source_origin"
            class="vue-word-embed"
          /> -->
        </div>
        <div class="report-box" v-if="isFixHistory">
          <div class="report-box-list" v-for="(item, nickName) in reviewReport" :key="nickName">
            <p class="card-header">{{ t("wordPreview.userRevisionRecord", { nickName }) }}</p>
            <div class="card-item" v-for="(reviewItem, index) in item" :key="reviewItem.Value">
              <div class="card-item-container" v-if="typeNameToZh[reviewItem.Type]">
                <span class="no">{{ index + 1 }}、</span>
                <span class="type">{{ typeNameToZh[reviewItem.Type] }}：</span>
                <span class="text" v-if="reviewItem.Value" :title="reviewItem.Value">{{ reviewItem.Value }}</span>
                <XButton preIcon="ep:position" size="small" type="primary" circle
                  @click="scrollToParagraph(reviewItem.Id, reviewItem.Date)" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="word-flex-wrap">
        <!-- <div class="popout-btn" @click="popout('right')">弹出到新窗口</div> -->
        <div class="word-wrap" :style="{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: `translate(-50%,-50%) scale(${state.scale})`,
          width: `${pageWidth}`,
          height: `${pageHeight}`,
        }">
          <!-- <word-preview-single
            ref="wordPreviewSingleRight"
            officeIdName="wordPreviewSingleRight"
            :updateTime="props.updateTime"
            :currentPage="state.pageNum"
            :fileName="fileNameStr"
            :fileUrl="state.source_translate"
            class="vue-word-embed"
          /> -->
          <DocumentEditor id="target" ref="wordPreviewSingleRight" :documentServerUrl="VITE_ONLY_OFFICE_BASE_URL"
            :config="targetConfig" :events_onDocumentReady="onDocumentReadyTarget" />
        </div>
        <div class="report-box" v-if="isFixHistory">
          <div class="report-box-list" v-for="(item, nickName) in reviewReport_target" :key="nickName">
            <p class="card-header">{{ t("wordPreview.userRevisionRecord", { nickName }) }}</p>
            <div class="card-item" v-for="(reviewItem, index) in item" :key="reviewItem.Value">
              <div class="card-item-container" v-if="typeNameToZh[reviewItem.Type]">
                <span class="no">{{ index + 1 }}、</span>
                <span class="type">{{ typeNameToZh[reviewItem.Type] }}：</span>
                <span class="text" v-if="reviewItem.Value" :title="reviewItem.Value">{{ reviewItem.Value }}</span>
                <XButton preIcon="ep:position" size="small" type="primary" circle
                  @click="scrollToParagraph_target(reviewItem.Id, reviewItem.Date)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="page-tool">
      <!-- <div class="page-tool-item" @click="jumpTo(1)">{{ t('wordPreview.paragraphEdit') }}</div> -->
      <div class="page-tool-item active">{{ t("wordPreview.formatEdit") }}</div>
      <div class="page-tool-item" @click="toToggleWrap">
        {{ toggleWrap ? t("wordPreview.leftRightLayout") : t("wordPreview.topBottomLayout") }}
      </div>
      <!-- <div class="page-tool-item" @click="jumpTo(2)">{{ t('wordPreview.sentenceComparison') }}</div> -->
      <!-- <div class="page-tool-item" @click="lastPage">{{ t('wordPreview.previousPage') }}</div>
      <div class="page-tool-item" @click="nextPage">{{ t('wordPreview.nextPage') }}</div> -->
      <!-- <div class="page-tool-item">{{ state.pageNum }}/{{ state.numPages }}</div> -->
      <!-- <div class="page-tool-item" @click="pageZoomOut">{{ t('wordPreview.zoomIn') }}</div>
      <div class="page-tool-item" @click="pageZoomIn">{{ t('wordPreview.zoomOut') }}</div>
      <div class="page-tool-item" @click="pageRest">{{ t('wordPreview.resetZoom') }}</div> -->
      <div class="page-tool-item" @click="toggleComparison">{{ isComparison ? t("wordPreview.closeComparison") :
        t("wordPreview.openComparison") }}</div>
      <div class="page-tool-item" @click="toggleFixHistory" v-if="reviewMode">{{ isFixHistory ?
        t("wordPreview.closeRevisionRecord") :
        t("wordPreview.openRevisionRecord") }}</div>
      <div class="page-tool-item" @click="downloadWord('origin')">{{ t("wordPreview.downloadOriginal") }}</div>
      <div class="page-tool-item" @click="downloadWord('source')">{{ t("wordPreview.downloadTranslation") }}</div>
      <div class="page-tool-item" @click="toShowSaveVersion">{{ t("wordPreview.uploadVersion") }}</div>
    </div>

    <Dialog v-model="dialogFormVisible" :title="t('wordPreview.retranslate')" :width="800"
      :close-on-click-modal="false">
      <el-form>
        <el-form-item :label="t('wordPreview.original')" :label-width="formLabelWidth">
          <el-input v-model="formData.zhCnText" autocomplete="off" :rows="6" type="textarea" />
        </el-form-item>
        <el-form-item :label="t('wordPreview.oldTranslation')" :label-width="formLabelWidth">
          <el-input v-model="formData.engText" autocomplete="off" :rows="6" type="textarea" disabled />
        </el-form-item>
        <el-row>
          <el-col :span="24">
            <el-form-item label="" :label-width="formLabelWidth">
              <el-button class="retranslate" type="primary" size="mini" @click="handleRetranslate"
                :loading="retranslateLoading">
                <Icon icon="ep:refresh" class="mr-5px" />
                {{ t("wordPreview.retranslate") }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="t('wordPreview.newTranslation')" :label-width="formLabelWidth">
          <el-input v-model="formData.replaceText" autocomplete="off" :rows="6" type="textarea" />
        </el-form-item>
        <el-row>
          <el-col :span="24">
            <el-form-item label="" :label-width="formLabelWidth">
              <el-button class="retranslate" type="primary" size="mini" @click="copyNewTranslate">
                <Icon icon="ep:copy-document" class="mr-5px" />
                {{ t("wordPreview.copyNewTranslation") }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer text-center">
          <el-button type="primary" @click="replaceTargetTextByTargetId">{{ t("wordPreview.confirmReplaceTranslation")
          }}</el-button>
          <el-button @click="dialogFormVisible = false">{{ t("wordPreview.cancel") }}</el-button>
        </div>
      </template>
    </Dialog>

    <EditForm ref="editFormRef" whichPannel="right" />
  </div>
  <SaveVersion ref="saveVersionRef" :transId="id" />
</template>
<script lang="ts" setup>
import { unRef } from "vue";
import { DICT_TYPE, getDictOptions } from "@/utils/dict";
import { DocTranslateApi } from "@/api/system/translate";
import { getUserProfile } from "@/api/system/user/profile";
import WordPreviewSingle from "./WordPreviewSingle.vue";
import { DocumentEditor } from "@onlyoffice/document-editor-vue";
import { debounce } from "@/utils/index";
import EditForm from "@/components/TextPreview/EditForm.vue";
import SaveVersion from "@/components/SaveVersion/index.vue";
import imgLogo from "@/assets/imgs/logo.png";
import { ElMessage, ElMessageBox } from "element-plus";
import { copyTextToClipboard } from "@/utils/index";
import { checkFileType } from "@/utils/check-file-type";
import { useRoute } from "vue-router";
import { computed } from "vue";
import { i18n } from "@/plugins/vueI18n";
import { useI18n } from "vue-i18n";

const props = defineProps({
  wordUrlList: {
    type: Array,
    required: true,
  },
  fileName: {
    type: String,
    required: true,
  },
  updateTime: {
    type: Number,
    required: true,
  },
  scene: {
    type: Number,
    required: true,
  },
});

const { t } = useI18n();
const curLang = computed(() => i18n.global.locale);

const emit = defineEmits(["toShowTable"]);
const toShowTable = () => {
  emit("toShowTable");
};

// 上传版本
const saveVersionRef = ref();
const toShowSaveVersion = () => {
  saveVersionRef.value.open();
};

const router = useRouter();
const { id } = router.currentRoute.value.query;

const userInfo = ref({});

const editFormRef = ref();

const toggleIcon = ref(false);

const dialogVisible = ref(true);
const formLabelWidth = "80px";
const dialogFormVisible = ref(false);
const formData = ref({
  zhCnText: "",
  engText: "",
  replaceText: "",
  remark: "",
});

// 高亮对照开关
const isComparison = ref(false);
// 修订记录开关
const isFixHistory = ref(false);

// 语法
// const questions_grammar = ref([]);
// questions_grammar.value = getDictOptions(DICT_TYPE.TRANSLATE_GRAMMAR).map((s) => s.label);
//  模板
// const questions_template = ref([]);
// questions_template.value = getDictOptions(DICT_TYPE.TRANSLATE_TEMPLATE_FORMAT).map((s) => s.label);

// 关键词
const checkList = ref<number[]>([]);
const keyWords = ref([]);
keyWords.value = getDictOptions(DICT_TYPE.TRANSLATE_KEYWORDS).map((s) => s.label);

// 关键词高亮
const changeCheckList = (data: number[]) => {
  Asc.scope = {
    keyWords: keyWords.value,
  };
  if (data[0] === 0) {
    targetConnector.callCommand(
      () => {
        let doc = Api.GetDocument();
        Asc.scope.keyWords.forEach((keyWord) => {
          console.log(keyWord)
          // doc.GetRangeByString(keyWord).SetHighlight("yellow");
          let searchResults = doc.Search(keyWord);
          searchResults.forEach((result, index) => {
            console.log(result)
            // result.SetColor(255, 49, 0);
            result.SetHighlight("yellow");
            // result.SetUnderline(true);
            // result.SetBold(true);
            // result.AddBookmark(`Bookmark_${keyWord}_${index}`);
          });
        });

        // Api.openBookmarkHighLight();
      },
      () => { }
    );
  } else {
    targetConnector.callCommand(
      function () {
        let doc = Api.GetDocument();
        Asc.scope.keyWords.forEach((keyWord) => {
          console.log(keyWord)
          // doc.GetRangeByString(keyWord).SetHighlight("yellow");
          let searchResults = doc.Search(keyWord);
          searchResults.forEach((result, index) => {
            console.log(result)
            // result.SetColor(0, 0, 0);
            result.SetHighlight("white");
            // result.SetUnderline(false);
            // result.SetBold(false);
            // result.AddBookmark(`Bookmark_${keyWord}_${index}`);
          });
        });
        // Api.closeBookmarkHighLight();
      },
      function () { }
    );
  }
};

// 审阅模式开关
const modeList = ref<number[]>([]);
const reviewModeValue = localStorage.getItem('curReviewMode') === '1';
if (reviewModeValue) modeList.value[0] = 1;
const reviewMode = ref(reviewModeValue);
const changeMode = (data: number[]) => {
  if (data[0] === 1) {
    localStorage.setItem('curReviewMode', '1');
    reviewMode.value = true;
  } else {
    localStorage.setItem('curReviewMode', '0');
    reviewMode.value = false;
  }
}

// 排版切换
const toggleWrapStorage = JSON.parse(localStorage.getItem("toggleWrapStorage") || "false");
const toggleWrap = ref(toggleWrapStorage);
const toToggleWrap = () => {
  toggleWrap.value = !toggleWrap.value;
  localStorage.setItem("toggleWrapStorage", String(toggleWrap.value));
  calcDocHeight();
};

// 文档高度
const docHeight = ref(0);
const calcDocHeight = () => {
  const coeff = toggleWrap.value ? 2 : 1;
  docHeight.value = (innerHeight - 260) / coeff + "px";
};
calcDocHeight();

const getUserInfo = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      const user = await getUserProfile();
      resolve(user);
    } catch (error) {
      console.error(t("wordPreview.getUserInfoFailed"), error);
      reject(error);
    }
  });
};

const wordPreviewSingleRight = ref();

const jumpTo = (type: number) => {
  if (type === 1) {
    router.push({ path: "/translate/preview/text", query: { id } });
  } else if (type === 2) {
    router.push({ path: "/translate/preview/sentence", query: { id } });
  }
};

const getFileType = (url?: string) => {
  if (!url) return "";
  return url.split(".").pop()?.toLowerCase();
};

const state = reactive({
  source_origin: props.wordUrlList[0] || "", // 预览p文件地址
  source_translate: props.wordUrlList[1] || "", // 预览文件地址
  pageNum: 1, //当前页面
  scale: 1, // 缩放比例
  numPages: 0, // 总页数
  loading: true, //加载
  documentType_origin: checkFileType(props.wordUrlList[0] || ""),
  fileType_origin: getFileType(props.wordUrlList[0] || ""),
  documentType_translate: checkFileType(props.wordUrlList[1] || ""),
  fileType_translate: getFileType(props.wordUrlList[1] || ""),
});

// 下载文件
function downloadFile(url: string, type?: string) {
  const ext = url.split(".").pop()?.toLowerCase();
  const fileName = props.fileName.split(".").shift();
  const downLoadFileName = type === "origin" ? `${fileName}.${ext}` : `${fileName}${t("wordPreview.translationSuffix")}.${ext}`;
  fetch(encodeURI(url)).then((res) => {
    res.blob().then((myBlob) => {
      const href = URL.createObjectURL(myBlob);
      const a = document.createElement("a");
      a.href = href;
      a.download = downLoadFileName; // 下载文件重命名，并指定文件扩展名为 ".docx"
      document.body.appendChild(a); // 将<a>元素添加到文档中，以便进行点击下载
      a.click();
      document.body.removeChild(a); // 下载完成后移除<a>元素
      URL.revokeObjectURL(href);
    });
  });
}

const downloadWord = async (type: string) => {
  // 前端生成
  //  wordPreviewSingleRight.value.downloadFile();
  // 后端下载
  const data = await DocTranslateApi.getDocTranslate(id);
  if (type === "origin") {
    downloadFile(data.fileUrlOrigin, "origin");
  } else {
    downloadFile(data.fileUrlTranslate);
  }
};

// onMounted(() => {
// state.loading = true; // 添加一个loading状态
// });

const pageHeight = ref("100%");
const pageWidth = ref("100%");

function pageZoomOut() {
  if (state.scale < 2) {
    state.scale += 0.1;
    pageHeight.value = parseInt(pageHeight.value) - 5.0 + "%";
    pageWidth.value = parseInt(pageWidth.value) - 5.0 + "%";
  }
}

function pageZoomIn() {
  if (state.scale > 1) {
    state.scale -= 0.1;
    pageHeight.value = parseInt(pageHeight.value) + 5.0 + "%";
    pageWidth.value = parseInt(pageWidth.value) + 5.0 + "%";
  }
}

const pageRest = () => {
  state.scale = 1;
  pageHeight.value = "100%";
  pageWidth.value = "100%";
};

const fileNameStr = computed(() => {
  const ext = props.fileName.split(".").pop()?.toLowerCase();
  return props.fileName.replace(`.${ext}`, `${t("wordPreview.translationSuffix")}.${ext}`);
});

const getUuid = function () {
  return "xxxxxxxxxxxxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

const VITE_ONLY_OFFICE_BASE_URL = import.meta.env.VITE_ONLY_OFFICE_BASE_URL;
const VITE_ONLY_OFFICE_CALLBACK_URL = import.meta.env.VITE_ONLY_OFFICE_CALLBACK_URL;

onMounted(async () => {
  await getUserInfo().then((res) => {
    userInfo.value = {
      id: res.id + "",
      name: res.nickname || "",
      email: res.email || "",
      avatar: res.avatar || "",
    };
    state.loading = false;
  });

  // resize 修改docHeight
  window.addEventListener("resize", calcDocHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", calcDocHeight);

  // 取消定时器
  // if (curAutoSaveTimer) {
  //   clearInterval(curAutoSaveTimer);
  //   curAutoSaveTimer = null;
  // }
});

let sourceConnector = null;
let targetConnector = null;
let sourceClickParagraphId = null;
let targetClickParagraphId = null;

const getFileUrlName = (fileUrl: string) => {
  return fileUrl.split("/").pop()?.split(".")[0];
};

// 文档KEY 和后台约定的格式 ！！！ 不可修改
const sourceFileKey = ref(`${getFileUrlName(props.wordUrlList[0])}_origin_${id}`);
const targetFileKey = ref(`${getFileUrlName(props.wordUrlList[1])}_translate_${id}`);

// const sourceFileKey = `${getUuid()}_origin_${id}`;
// const targetFileKey = `${getUuid()}_translate_${id}`;

// console.log("sourceFileKey", sourceFileKey.value);
// console.log("targetFileKey", targetFileKey.value);

const retranslateLoading = ref(false);
const handleRetranslate = async () => {
  if (!formData.value.zhCnText) return;
  retranslateLoading.value = true;
  await DocTranslateApi.textTranslate({
    from: "zh",
    to: "en",
    content: formData.value.zhCnText,
    isCache: true,
    scene: props.scene || 1,
    type: 1, // type  1：中翻英  2：英翻中
    source: 1, // 1：本系统   2：argus系统  3：App
  }).then((res) => {
    retranslateLoading.value = false;
    formData.value.replaceText = res?.content || '';
  });
};

const copyNewTranslate = () => {
  if (!formData.value.replaceText) return;
  copyTextToClipboard(formData.value.replaceText);
  ElMessage({
    type: "success",
    message: t("wordPreview.copySuccess"),
  });
};

// 原文右键选中的文字
// const sourceSelectedText = ref("");
// 翻译的文字
// const translateText = ref("翻译中...");

// 重新翻译
// const handleRetranslate = async () => {
//   if (!sourceSelectedText.value) return;
//   retranslateLoading.value = true;
//   await DocTranslateApi.textTranslate({
//     from: "zh",
//     to: "en",
//     content: sourceSelectedText.value,
//     isCache: true,
//     scene: props.scene || 1,
//   }).then((res) => {
//     retranslateLoading.value = false;
//     translateText.value = res?.content || '';
//   });
// };

// const showAutoTranslate = async () => {
//   await handleRetranslate();
//   ElMessageBox.alert(translateText.value, "翻译结果", {
//     // if you want to disable its autofocus
//     // autofocus: false,
//     confirmButtonText: "复制",
//     callback: (action) => {
//       if (action === "confirm") {
//         copyTextToClipboard(translateText.value);
//         ElMessage({
//           type: "success",
//           message: "复制成功",
//         });
//       }
//     },
//   });
// };


const onDocumentReadySource = () => {
  sourceConnector = DocEditor.instances.source.createConnector();
  sourceConnector.attachEvent("onClickParagraph", function (data) {
    if (data.paragraphId) {
      console.log("left:" + data.paragraphId);
      sourceClickParagraphId = data.paragraphId;
      targetClickParagraphId = data.paragraphId;
      sourceFindParagraphIndex(data.paragraphId);
    }
  });

  // 保存监听
  // sourceConnector.attachEvent("onSaveComplete", async (isSuccess) => {
  //   const data = await DocTranslateApi.getDocTranslate(id);
  //   sourceFileKey.value = `${getFileUrlName(state.source_origin)}_${id}`;
  //   state.source_origin = data.fileUrlOrigin || ""; // 预览pdf文件地址
  //   // state.source_translate = data.fileUrlTranslate || ""; // 预览pdf文件地址
  //   // targetFileKey.value = `${id}_${getFileUrlName(state.source_translate)}`;
  //   console.log("callback保存完成：", isSuccess);
  // });

  // 记录滚动位置 - 防抖
  sourceConnector.attachEvent(
    "onScrollAction",
    debounce((data) => {
      localStorage.setItem(`scrollPositionLeft_${id}`, JSON.stringify(data));
    })
  );

  const scrollPositionLeft = localStorage.getItem(`scrollPositionLeft_${id}`);
  if (scrollPositionLeft) {
    const { type = "vertical", currentPos = 0, limit = 0 } = JSON.parse(scrollPositionLeft);
    Asc.scope = {
      type,
      currentPos,
      limit,
    };
    sourceConnector.callCommand(
      function () {
        Api.ScrollAction("toPos", Asc.scope.type, Asc.scope.currentPos, Asc.scope.limit);
      },
      function () { }
    );
  }

  // sourceConnector.attachEvent("onContextMenuShow", (options) => {
  //   if (options.type === "Selection") {
  //     sourceConnector.addContextMenuItem([
  //       {
  //         type: "Selection",
  //         id: "context_menu_id1",
  //         text: "重新翻译",
  //         onClick: () => {
  //           sourceConnector.callCommand(
  //             function () {
  //               var oDocument = Api.GetDocument();
  //               var oRange = oDocument.GetRangeBySelect();
  //               return oRange.GetText();
  //             },
  //             function (sourceText) {
  //               sourceSelectedText.value = sourceText;
  //               showAutoTranslate();
  //             }
  //           );
  //         },
  //       },
  //     ]);
  //   }
  // });

  sourceConnector.attachEvent("onContextMenuShow", (options) => {
    if (sourceClickParagraphId) {
      sourceConnector.addContextMenuItem([
        {
          id: "context_menu_id",
          text: t("wordPreview.retranslate"),
          data: sourceClickParagraphId,
          onClick: (data) => {
            fillSourceTextBySourceId(data);
          },
        },
      ]);
    }
  });

  sourceConnector.callCommand(
    function () {
      Api.openParagraphChange();
    },
    function () {
      sourceConnector.attachEvent("onParagraphChange", function (data) {
        initReviewReport();
      });
    }
  );

  initReviewReport();
};

// let curAutoSaveTimer = null;
const onDocumentReadyTarget = () => {
  targetConnector = DocEditor.instances.target.createConnector();

  targetConnector.attachEvent("onClickParagraph", function (data) {
    if (data.paragraphId) {
      console.log("right:" + data.paragraphId);
      targetClickParagraphId = data.paragraphId;
      targetFindParagraphIndex(data.paragraphId);
    }
  });

  // 保存监听
  // targetConnector.attachEvent("onSaveComplete", async function (isSuccess) {
  //   console.log(1);
  //   const data = await DocTranslateApi.getDocTranslate(id);
  //   console.log(data);
  //   targetFileKey.value = `${getFileUrlName(state.source_translate)}_${id}`;
  //   state.source_translate = data.fileUrlTranslate || ""; // 预览pdf文件地址
  //   console.log("callback保存完成：", isSuccess);
  // });

  // 记录滚动位置 - 防抖
  targetConnector.attachEvent(
    "onScrollAction",
    debounce((data) => {
      localStorage.setItem(`scrollPositionRight_${id}`, JSON.stringify(data));
    })
  );
  const scrollPositionRight = localStorage.getItem(`scrollPositionRight_${id}`);

  if (scrollPositionRight) {
    // 恢复滚动位置
    const { type = "vertical", currentPos = 0, limit = 0 } = JSON.parse(scrollPositionRight);
    Asc.scope = {
      type,
      currentPos,
      limit,
    };
    targetConnector.callCommand(
      function () {
        Api.ScrollAction("toPos", Asc.scope.type, Asc.scope.currentPos, Asc.scope.limit);
      },
      function () { }
    );
  }

  targetConnector.attachEvent("onContextMenuShow", (options) => {
    if (targetClickParagraphId) {
      targetConnector.addContextMenuItem([
        {
          id: "context_menu_id",
          text: t("wordPreview.problemReport"),
          data: targetClickParagraphId,
          onClick: (data) => {
            fillTargetTextByTargetId(data);
          },
        },
      ]);
    }
  });

  targetConnector.callCommand(
    function () {
      Api.openParagraphChange();
    },
    function () {
      targetConnector.attachEvent("onParagraphChange", function (data) {
        initReviewReport_target();
      });
    }
  );

  // 定时 自动保存
  // curAutoSaveTimer = setInterval(() => {
  //   targetConnector.callCommand(
  //     function () {
  //       var oDocument = Api.GetDocument();
  //       oDocument.forceSave2();
  //     },
  //     function () {
  //     }
  //   );
  // }, 10000);

  initReviewReport_target();
};

const customization = ref({
  // 自动保存可以关闭，常规ctrl+s更好用
  autosave: true,
  forcesave: true,
  help: false,
  about: false,

  hideNotes: true,
  hideRightMenu: true,
  hideRulers: true,

  macros: false,
  comments: false,
  mentionShare: false,
  compactHeader: true,
  compactToolbar: true,
  compatibleFeatures: true,
  toolbarHideFileName: true,

  feedback: false,
  plugins: true,

  // layout: {
  //   header: {
  //     editMode: false,
  //     save: false,
  //     users: false,
  //   },
  //   leftMenu: {
  //     mode: false,
  //     navigation: true,
  //     spellcheck: true,
  //   },
  //   rightMenu: {
  //     mode: false,
  //   },
  //   statusBar: false,
  //   toolbar: {
  //     collaboration: {
  //       mailmerge: false,
  //     },
  //     draw: false,
  //     file: {
  //       close: true,
  //       info: true,
  //       save: true,
  //       settings: true,
  //     },
  //     home: {},
  //     layout: true,
  //     plugins: false,
  //     data: false,
  //     protect: false,
  //     references: true,
  //     save: true,
  //     view: {
  //       navigation: true,
  //     },
  //   },
  // },

  logo: {
    // logo配置
    image: imgLogo,
    imageDark: imgLogo,
    // url: "http://36.156.124.58:8051",
    visible: false,
  },
  // features: {
  //   roles: false,
  //   spellcheck: {
  //     mode: false,
  //     change: false,
  //   },
  //   tabBackground: {
  //     mode: "toolbar",
  //     change: true,
  //   },
  //   tabStyle: {
  //     mode: "line",
  //     change: true,
  //   },
  // },
  review: {
    trackChanges: true, // 无论 document.permissions.review 参数如何，定义是否以审阅编辑模式 (true) 打开文档 (false) （审阅模式仅针对当前用户更改）。如果参数 未定义，则使用 document.permissions.review 值（对于所有文档用户）。
    // hideReviewDisplay: false, // 定义 显示模式 按钮是在 协作 选项卡上显示还是隐藏。
    // hoverMode: false, // 定义审阅显示模式：通过将鼠标悬停在更改上来在工具提示中显示审阅（true），或通过单击更改来在气球中显示审阅（false）。
    // reviewDisplay: "original",  // { markup - 显示文档，并突出显示建议的更改； simple - 显示文档并突出显示建议的更改，但气球已关闭； final - 显示文档并应用了所有建议的更改； original - 显示原始文档，没有建议的更改。 }
    // showReviewChanges: false, // 定义在加载编辑器时是否自动显示或隐藏审阅更改面板。默认值为 false。
  },
});

const editorConfigOirgin = ref({
  mode: "edit", // edit、view
  // callbackUrl: "https://api.docs.onlyoffice.com/dummyCallback",
  callbackUrl: `${VITE_ONLY_OFFICE_CALLBACK_URL}?id=${id}&type=origin&url=${state.source_origin}`,
  // lang: "zh-CN",
  lang: curLang.value,
});

const editorConfigTranslate = ref({
  mode: "edit", // edit、view
  // callbackUrl: "https://api.docs.onlyoffice.com/dummyCallback",
  callbackUrl: `${VITE_ONLY_OFFICE_CALLBACK_URL}?id=${id}&type=translate&url=${state.source_translate}`,
  lang: curLang.value,
});

let sourceConfig = computed(() => ({
  type: "desktop",
  // "type": "mobile",
  documentType: state.documentType_origin,
  // historyList: {
  //   history: [],
  //   currentVersion: "1",
  // },
  document: {
    title: props.fileName,
    url: state.source_origin,
    permissions: {
      print: false,
      download: true,
      review: reviewMode.value,
      // edit: true,
    },
    fileType: state.fileType_origin,
    key: sourceFileKey.value,
  },
  editorConfig: {
    user: userInfo.value,
    customization: customization.value,
    ...editorConfigOirgin.value,
  },
}));

var targetConfig = computed(() => ({
  type: "desktop",
  // "type": "mobile",
  documentType: state.documentType_translate,
  // historyList: {
  //   history: [],
  //   currentVersion: "1",
  // },
  document: {
    title: fileNameStr.value,
    url: state.source_translate,
    permissions: {
      print: false,
      download: true,
      review: reviewMode.value,
      // edit: true,
    },
    fileType: state.fileType_translate,
    key: targetFileKey.value,
  },
  editorConfig: {
    user: userInfo.value,
    customization: customization.value,
    ...editorConfigTranslate.value,
  },
}));

// 切换对照插件
const toggleComparison = () => {
  isComparison.value = !isComparison.value;
  if (!isComparison.value) {
    clearnLastHighLight();
  }
};

const toggleFixHistory = () => {
  isFixHistory.value = !isFixHistory.value;
  if (!isFixHistory.value) {
    clearnLastHighLight();
  }
};

let sourceFindParagraphIndex = function (paragraphId) {
  if (!isComparison.value) return;
  Asc.scope = {
    paragraphId: paragraphId,
  };
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      // console.log(oDocument.GetElement(2));
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraphIndex = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        var paragraph = allParagraphs[i];
        if (Asc.scope.paragraphId == paragraph.GetId()) {
          // console.log(paragraph.GetId(), oDocument.GetElement(i));
          paragraphIndex = i;
          break;
        }
      }
      return paragraphIndex;
    },
    function (paragraphIndex) {
      if (paragraphIndex != null) {
        // 原文点击的时候，同步更新一下targetClickParagraphId
        Asc.scope = {
          paragraphIndex: paragraphIndex,
        };
        targetConnector.callCommand(
          function () {
            var oDocument = Api.GetDocument();
            var allParagraphs = oDocument.GetAllParagraphs();
            var paragraphId = null;
            for (var i = 0; i < allParagraphs.length; i++) {
              var paragraph = allParagraphs[i];
              if (Asc.scope.paragraphIndex == i) {
                paragraphId = paragraph.GetId();
                break;
              }
            }
            return paragraphId;
          },
          function (paragraphId) {
            if (paragraphId != null) {
              targetClickParagraphId = paragraphId;
              highLightParagraph(sourceConnector, paragraphIndex);
              highLightParagraph(targetConnector, paragraphIndex);
            }
          }
        );
      }
    }
  );
};

let targetFindParagraphIndex = function (paragraphId) {
  if (!isComparison.value) return;
  Asc.scope = {
    paragraphId: paragraphId,
  };
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraphIndex = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        var paragraph = allParagraphs[i];
        if (Asc.scope.paragraphId == paragraph.GetId()) {
          paragraphIndex = i;
          break;
        }
      }
      return paragraphIndex;
    },
    function (paragraphIndex) {
      if (paragraphIndex != null) {
        // 译文点击的时候，同步更新一下sourceClickParagraphId
        Asc.scope = {
          paragraphIndex: paragraphIndex,
        };
        sourceConnector.callCommand(
          function () {
            var oDocument = Api.GetDocument();
            var allParagraphs = oDocument.GetAllParagraphs();
            var paragraphId = null;
            for (var i = 0; i < allParagraphs.length; i++) {
              var paragraph = allParagraphs[i];
              if (Asc.scope.paragraphIndex == i) {
                paragraphId = paragraph.GetId();
                break;
              }
            }
            return paragraphId;
          },
          function (paragraphId) {
            if (paragraphId != null) {
              sourceClickParagraphId = paragraphId;
              highLightParagraph(sourceConnector, paragraphIndex);
              highLightParagraph(targetConnector, paragraphIndex);
            }
          }
        );
      }
    }
  );
};

/**
 * 设置段落高亮
 * 并且跳转到该位置
 **/
let highLightParagraph = function (connectorInstance, paragraphIndex) {
  Asc.scope = {
    paragraphIndex: paragraphIndex,
  };
  connectorInstance.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      if (oDocument.HighLightParagraphByIndex) {
        oDocument.HighLightParagraphByIndex("index", [Asc.scope.paragraphIndex], true);
      }
    },
    function (paragraphIndex) { }
  );
};

const clearnLastHighLight = function () {
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      if (oDocument.ResetHighLightParagraph) {
        oDocument.ResetHighLightParagraph();
      }
    },
    function (paragraphIndex) { }
  );
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      if (oDocument.ResetHighLightParagraph) {
        oDocument.ResetHighLightParagraph();
      }
    },
    function (paragraphIndex) { }
  );
};

/**
 * 通过原文ID设置原文的text
 * @param paragraphId 译文段落ID
 */
let fillSourceTextBySourceId = function (paragraphId) {
  Asc.scope = {
    paragraphId: paragraphId,
  };
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraphIndex = null;
      let text = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        var paragraph = allParagraphs[i];
        if (Asc.scope.paragraphId == paragraph.GetId()) {
          paragraphIndex = i;
          text = paragraph.GetText({
            Numbering: true,
            Math: true,
            NewLineSeparator: "\r",
            TabSymbol: "\t",
          });
          break;
        }
      }
      return { index: paragraphIndex, text: text };
    },
    function (resultData) {
      if (resultData.index == null) {
        return;
      }
      // 设置原文
      formData.value.zhCnText = resultData.text;
      // 设置译文
      fillTargetTextBySourceIndex(resultData.index);
    }
  );
};

/**
 * 通过译文ID设置译文的text
 * @param paragraphId 译文段落ID
 */
let fillTargetTextByTargetId = function (paragraphId) {
  Asc.scope = {
    paragraphId: paragraphId,
  };
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraphIndex = null;
      let text = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        var paragraph = allParagraphs[i];
        if (Asc.scope.paragraphId == paragraph.GetId()) {
          paragraphIndex = i;
          text = paragraph.GetText({
            Numbering: true,
            Math: true,
            NewLineSeparator: "\r",
            TabSymbol: "\t",
          });
          break;
        }
      }
      return { index: paragraphIndex, text: text };
    },
    function (resultData) {
      if (resultData.index == null) {
        return;
      }
      // 设置译文
      formData.value.engText = resultData.text;
      // 设置原文
      fillSourceTextByTargetIndex(resultData.index);
    }
  );
};

/**
 * 通过原文的index填充译文的text
 * @param sourceIndex 原文的index
 */
let fillTargetTextBySourceIndex = function (sourceIndex) {
  Asc.scope = {
    sourceIndex: sourceIndex,
  };
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraphIndex = null;
      let text = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        var paragraph = allParagraphs[i];
        if (Asc.scope.sourceIndex == i) {
          paragraphIndex = i;
          text = paragraph.GetText({
            Numbering: true,
            Math: true,
            NewLineSeparator: "\r",
            TabSymbol: "\t",
          });
          break;
        }
      }
      return { index: paragraphIndex, text: text };
    },
    function (resultData) {
      if (resultData.index == null) {
        return;
      }
      // 设置译文
      formData.value.engText = resultData.text;
      dialogFormVisible.value = true;
    }
  );
};

/**
 * 通过译文的index填充原文的text
 * @param targetIndex 译文的index
 */
let fillSourceTextByTargetIndex = function (targetIndex) {
  Asc.scope = {
    targetIndex: targetIndex,
  };
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraphIndex = null;
      let text = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        var paragraph = allParagraphs[i];
        if (Asc.scope.targetIndex == i) {
          paragraphIndex = i;
          text = paragraph.GetText({
            Numbering: true,
            Math: true,
            NewLineSeparator: "\r",
            TabSymbol: "\t",
          });
          break;
        }
      }
      return { index: paragraphIndex, text: text };
    },
    function (resultData) {
      if (resultData.index == null) {
        return;
      }
      // 设置原文
      formData.value.zhCnText = resultData.text;

      // dialogFormVisible.value = true;

      editFormRef.value.open(id, formData.value.zhCnText, formData.value.engText);
    }
  );
};

/**
 * 替换译文文字
 */
let replaceTargetTextByTargetId = function () {
  // console.log(targetClickParagraphId)
  if (!targetClickParagraphId) {
    ElMessage.error(t("wordPreview.noTranslationSelected"));
    return;
  }
  if (formData.value.replaceText == "") {
    ElMessage.error(t("wordPreview.newTranslationEmpty"));
    return;
  }
  Asc.scope = {
    paragraphId: targetClickParagraphId,
    replaceText: formData.value.replaceText,
  };
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraph = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        paragraph = allParagraphs[i];
        if (Asc.scope.paragraphId == paragraph.GetId()) {
          break;
        }
      }
      if (paragraph) {
        paragraph.SetText(Asc.scope.replaceText);
      }
    },
    function () {
      formData.value.zhCnText = "";
      formData.value.engText = "";
      formData.value.replaceText = "";
      dialogFormVisible.value = false;
      editFormRef.value.close();
    }
  );
};

const typeNameToZh = computed(() => ({
  TextAdd: t("wordPreview.textAdd"),
  TextRem: t("wordPreview.textRemove"),
  ParaAdd: t("wordPreview.paragraphAdd"),
  ParaRem: t("wordPreview.paragraphRemove"),
  TextPr: t("wordPreview.textStyleModify"),
  ParaPr: t("wordPreview.paragraphStyleModify"),
  Unknown: t("wordPreview.unknown"),
}));
// sourceConnector
//   targetConnector
const reviewReport = ref([]);
const reviewReport_target = ref([]);
const initReviewReport = () => {
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      let reviewReport = oDocument.GetReviewReport();
      for (var nickName in reviewReport) {
        for (var i = 0; i < reviewReport[nickName].length; i++) {
          var item = reviewReport[nickName][i];
          item["ClassType"] = item.ReviewedElement.GetClassType();
          item["Id"] = item.ReviewedElement.GetId();
          delete item["ReviewedElement"];
        }
      }
      return reviewReport;
    },
    function (reviewReportData) {
      reviewReport.value = reviewReportData;
    }
  );
};

const initReviewReport_target = () => {
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      let reviewReport = oDocument.GetReviewReport();
      for (var nickName in reviewReport) {
        for (var i = 0; i < reviewReport[nickName].length; i++) {
          var item = reviewReport[nickName][i];
          item["ClassType"] = item.ReviewedElement.GetClassType();
          item["Id"] = item.ReviewedElement.GetId();
          delete item["ReviewedElement"];
        }
      }
      return reviewReport;
    },
    function (reviewReportData) {
      reviewReport_target.value = reviewReportData;
    }
  );
};
// window.initReviewReport = initReviewReport;

const scrollToParagraph = function (id, date) {
  Asc.scope = {
    Id: id,
    Date: date,
  };
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      oDocument.SelectRevisionChange2(Asc.scope.Id, Asc.scope.Date);
    },
    function () { }
  );
};
const scrollToParagraph_target = function (id, date) {
  Asc.scope = {
    Id: id,
    Date: date,
  };
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      oDocument.SelectRevisionChange2(Asc.scope.Id, Asc.scope.Date);
    },
    function () { }
  );
};

function popout(which: "left" | "right") {
  const url = new URL(window.location.href);
  url.searchParams.set("popout", which);
  window.open(url.toString(), "_blank", "width=1200,height=900");
}

const route = useRoute();
const popoutQuery = computed(() => route.query.popout);

if (popoutQuery.value === "left") {
  // 只渲染左侧
} else if (popoutQuery.value === "right") {
  // 只渲染右侧
} else {
  // 正常渲染左右
}
</script>
<style lang="scss" scoped>
@use "../TextPreview/index.scss" as *;

.top-check-box {
  position: absolute;
  right: 70px;
  top: 0;
  display: flex;
  align-items: center;
  gap: 30px;
}

.toggle-show {
  position: absolute;
  right: 30px;
  top: 10px;
  cursor: pointer;

  &:hover {
    color: #2988f4;
  }
}

.showTable {
  color: #2988f4;
  font-size: 14px;
  text-align: right;
  height: 50px;
  line-height: 50px;
  padding-right: 20px;
  cursor: pointer;

  &:hover {
    color: #083e7b;
  }
}

.word-preview {
  height: 100%;
  position: relative;
}

.word-flex-box {
  height: 100%;
  display: flex;
  gap: 10px;
  justify-content: space-between;

  &.toggleWrap {
    flex-direction: column;

    .word-flex-wrap {}
  }
}

.word-flex-wrap {
  height: 100%;
  min-height: v-bind(docHeight);
  flex: 1;
  padding: 20px 0;
  position: relative;
  background-color: #e9e9e9;
  box-sizing: border-box;
  overflow: auto;
  border-radius: 8px;
}

.word-wrap {
  // overflow-y: auto;
}

.vue-word-embed {
  text-align: center;
  width: 100%;
  border: 1px solid #e5e5e5;
  margin: 0 auto;
  box-sizing: border-box;
}

.report-box {
  position: absolute;
  top: 32px;
  left: 0;
  width: 300px;
  height: calc(100% - 32px);
  background: rgba(255, 255, 255, 0.9);
  z-index: 999;
  padding: 0 10px;
  overflow-y: auto;

  .card-header {
    font-size: 16px;
    font-weight: 700;
    padding: 10px 0;
    border-bottom: 1px solid #ebebeb;
  }

  .card-item {
    padding: 10px;
    border-bottom: 1px dashed #ebebeb;

    .card-item-container {
      font-size: 14px;
      display: flex;
      align-items: center;

      .text {
        flex: 0 0 130px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.popout-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background: #2988f4;
  color: #fff;
  padding: 4px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}
</style>
