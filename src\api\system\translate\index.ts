import request from "@/config/axios";

// 文档翻译信息 VO
export interface DocTranslateVO {
  id: number; // 主键ID
  fileName: string; // 文件名称
  fileSize: number; // 文件大小
  fileUrlOrigin: string; // 文件地址
  fileUrlTranslate: string; // 译文地址
  type: string; // 翻译类型1：中文翻英文；2：英文翻中文
  status: number; // 翻译状态 0:翻译中；1:翻译成功；2:翻译失败
}

// 文档翻译信息 API
export const DocTranslateApi = {
  // 查询文档翻译信息分页
  getDocTranslatePage: async (params: any) => {
    return await request.get({ url: `/system/translate/page`, params });
  },

  // 查询文档翻译信息详情
  getDocTranslate: async (id: number) => {
    return await request.get({ url: `/system/translate/get?id=` + id });
  },

  // 分段 分句
  getDocSplit: async (id: number) => {
    return await request.get({ url: `/system/split/get?id=` + id });
  },


  // 保存文档
  saveDocx: async (id: number) => {
    return await request.get({ url: `/system/translate/saveDocx?id=` + id });
  },

  // 审核
  //  id 翻译ID
  // auditStatus: 审核状态  2：通过；3、不通过
  // reason  原因
  auditDocx: async (data) => {
    return await request.put({ url: `/system/translate/audit`, data });
  },


  // 新增文档翻译信息
  createDocTranslate: async (data: DocTranslateVO) => {
    return await request.post({ url: `/system/translate/create`, data });
  },

  // 新增文档翻译信息
  textTranslate: async (data: any) => {
    return await request.post({ url: `/system/translate/text`, data });
  },

  // 修改文档翻译信息
  updateDocTranslate: async (data: DocTranslateVO) => {
    return await request.put({ url: `/system/translate/update`, data });
  },

  // 删除文档翻译信息
  deleteDocTranslate: async (id: number) => {
    return await request.delete({ url: `/system/translate/delete?id=` + id });
  },

  // 导出文档翻译信息 Excel
  exportDocTranslate: async (params) => {
    return await request.download({ url: `/system/translate/export-excel`, params });
  },


  downLoadDoc: async (params) => {
    return await request.download({ url: `/system/translate/downLoad`, params });
  },

  // 查询文本翻译 历史记录 分页
  getRecordTranslatePage: async (params: any) => {
    return await request.get({ url: `/system/translate/record/page`, params });
  },

  // 查询文本翻译 历史记录 详情
  getRecordTranslate: async (id: any) => {
    return await request.get({ url: `/system/translate/record/get?id=${id}` });
  },

  // 删除文本翻译 历史记录
  deleteRecordTranslate: async (id: number) => {
    return await request.delete({ url: `/system/translate/record/delete?id=` + id });
  },

  // 修改 文本翻译 历史记录
  updateRecordTranslate: async (data) => {
    return await request.put({ url: `/system/translate/record/update`, data });
  },

  // 导出 文本翻译 历史记录 Excel
  exportRecordTranslate: async (params) => {
    return await request.download({ url: `/system/translate/record/export-excel`, params });
  },

};

export const uploadFile = (data: any) => {
  return request.upload({ url: "/system/translate/upload", data });
};

