<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item :label="t('translate.versionForm.fileName')" prop="fileName" required>
        <el-input v-model="formData.fileName" :placeholder="t('translate.versionForm.fileNamePlaceholder')" />
      </el-form-item>
      <!-- <el-form-item :label="t('translate.versionForm.currentVersion')" prop="version">
        <el-input v-model="formData.version" :placeholder="t('translate.versionForm.enterCurrentVersion')" />
      </el-form-item> -->
      <el-form-item :label="t('translate.versionForm.uploadOriginal')" prop="origin" required>
        <upload-file v-model="formData.origin"
          @success="(url, fileName) => handleUploadSuccess(url, 'origin', fileName)" />
      </el-form-item>
      <el-form-item :label="t('translate.versionForm.uploadTranslation')" prop="translate" required>
        <upload-file v-model="formData.translate" @success="(url) => handleUploadSuccess(url, 'translate')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('translate.versionForm.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('translate.versionForm.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TranslateVersionApi, TranslateVersionVO } from '@/api/doc/translateversion'
import UploadFile from './uploadFile.vue'

/** 翻译版本信息 表单 */
defineOptions({ name: 'TranslateVersionForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  fileName: undefined,
  origin: undefined,
  translate: undefined,
  isStudy: '0',
  // version: '1.0.0',
  source: '2',
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

const handleUploadSuccess = (url: string, type: string, fileName?: string) => {
  formData.value[type] = url;
  if (fileName) {
    formData.value.fileName = fileName;
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TranslateVersionApi.getTranslateVersion(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TranslateVersionVO
    if (formType.value === 'create') {
      await TranslateVersionApi.createTranslateVersion(data)
      message.success(t('common.createSuccess'))
    } else {
      await TranslateVersionApi.updateTranslateVersion(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    fileName: undefined,
    origin: undefined,
    translate: undefined,
    isStudy: '0',
    // version: '1.0.0',
    source: '2',
  }
  formRef.value?.resetFields()
}
</script>