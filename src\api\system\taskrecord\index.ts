import request from '@/config/axios'

// 维保记录 VO
export interface TaskRecordVO {
  id: number // id
  entId: number // 单位ID
  deptId: number // 部门ID
  serviceDesc: string // 服务描述
  serviceImg: string // 服务图片列表
  reportDesc: string // 报告描述
  reportImg: string // 报告图片列表
  status: number // 审核状态： 0：待审核；1：已审核；
  staff: string // 服务人员姓名
  manager: string // 项目经理姓名
}

// 维保记录 API
export const TaskRecordApi = {
  // 查询维保记录分页
  getTaskRecordPage: async (params: any) => {
    return await request.get({ url: `/system/task-record/page`, params })
  },

  // 查询维保记录详情
  getTaskRecord: async (id: number) => {
    return await request.get({ url: `/system/task-record/get?id=` + id })
  },

  // 新增维保记录
  createTaskRecord: async (data: TaskRecordVO) => {
    return await request.post({ url: `/system/task-record/create`, data })
  },

  // 修改维保记录
  updateTaskRecord: async (data: TaskRecordVO) => {
    return await request.put({ url: `/system/task-record/update`, data })
  },

  // 删除维保记录
  deleteTaskRecord: async (id: number) => {
    return await request.delete({ url: `/system/task-record/delete?id=` + id })
  },

  // 导出维保记录 Excel
  exportTaskRecord: async (params) => {
    return await request.download({ url: `/system/task-record/export-excel`, params })
  },
}
