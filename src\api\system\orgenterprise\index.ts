import request from '@/config/axios'

// 单位底册 VO
export interface OrgEnterpriseVO {
  id: number // id
  name: string // 单位名称
  address: string // 单位地址
  district: string // 所属县区
  grade: string // 单位等级
  leader: string // 单位负责人
  leaderPhone: string // 负责人联系方式
  buildSpace: string // 建筑面积
  lastTime: Date // 最后一次维保时间
}

// 单位底册 API
export const OrgEnterpriseApi = {
  // 查询单位底册分页
  getOrgEnterprisePage: async (params: any) => {
    return await request.get({ url: `/system/org-enterprise/page`, params })
  },

  // 查询单位底册详情
  getOrgEnterprise: async (id: number) => {
    return await request.get({ url: `/system/org-enterprise/get?id=` + id })
  },

  // 新增单位底册
  createOrgEnterprise: async (data: OrgEnterpriseVO) => {
    return await request.post({ url: `/system/org-enterprise/create`, data })
  },

  // 修改单位底册
  updateOrgEnterprise: async (data: OrgEnterpriseVO) => {
    return await request.put({ url: `/system/org-enterprise/update`, data })
  },

  // 删除单位底册
  deleteOrgEnterprise: async (id: number) => {
    return await request.delete({ url: `/system/org-enterprise/delete?id=` + id })
  },

  // 导出单位底册 Excel
  exportOrgEnterprise: async (params) => {
    return await request.download({ url: `/system/org-enterprise/export-excel`, params })
  },
}
