<template>
  <Dialog v-model="dialogVisible" :title="t('docFileForm.uploadFile')">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="uploadUrl"
      :auto-upload="false"
      :data="data"
      :disabled="formLoading"
      :limit="1000"
      multiple
      :on-change="handleFileChange"
      :on-error="submitFormError"
      :on-exceed="handleExceed"
      :on-success="submitFormSuccess"
      :http-request="httpRequest"
      accept=".pdf"
      drag
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">{{ t('docFileForm.dragOrClickUpload') }}</div>
      <template #tip>
        <div class="el-upload__tip" style="color: red">
          {{ t('docFileForm.pdfOnlyTip') }}
        </div>
      </template>
    </el-upload>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitFileForm">{{ t('common.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { useUpload2 } from "@/components/UploadFile/src/useUpload";
import { ElLoading } from "element-plus";
// 手动引入loading的css
import "element-plus/theme-chalk/el-loading.css";

defineOptions({ name: "InfraFileForm" });

const { t } = useI18n(); // 国际化
const message = useMessage(); // 消息弹窗

const dialogVisible = ref(false); // 弹窗的是否展示
const formLoading = ref(false); // 表单的加载中
const fileList = ref([]); // 文件列表
const data = ref({ path: "" });
const uploadRef = ref();
const { uploadUrl, httpRequest } = useUpload2();

const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `;

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true;
  resetForm();
};
defineExpose({ open }); // 提供 open 方法，用于打开弹窗

/** 处理上传的文件发生变化 */
const handleFileChange = (file) => {
  data.value.path = file.name;
};
const loadingInstance = ref();
/** 提交表单 */
const submitFileForm = () => {
  if (fileList.value.length == 0) {
    message.error(t('docFileForm.pleaseUploadFile'));
    return;
  }
  loadingInstance.value = ElLoading.service({
    fullscreen: true,
    text: t('docFileForm.aiAnalyzing'),
    background: "rgba(122, 122, 122, 0.8)",
  });
  unref(uploadRef)?.submit();
};

/** 文件上传成功处理 */
const emit = defineEmits(["success"]); // 定义 success 事件，用于操作成功后的回调
const submitFormSuccess = () => {
  // 清理
  dialogVisible.value = false;
  formLoading.value = false;
  unref(uploadRef)?.clearFiles();
  loadingInstance.value.close();
  // 提示成功，并刷新
  message.success(t("common.addSuccess"));
  emit("success");
};

/** 上传错误提示 */
const submitFormError = (): void => {
  loadingInstance.value.close();
  message.error(t('docFileForm.uploadFailed'));
  formLoading.value = false;
};

/** 重置表单 */
const resetForm = () => {
  // 重置上传状态和文件
  formLoading.value = false;
  uploadRef.value?.clearFiles();
};

/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error(t('docFileForm.maxFilesExceeded'));
};
</script>
