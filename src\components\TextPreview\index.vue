<template>
  <div class="choose-box">
    <!-- 关键词显示-绿色   语法问题显示-红色   模板格式问题显示-蓝色 -->
    <!-- 多选框 -->
    <p class="choose-title">译文问题显示</p>
    <el-checkbox-group v-model="checkList" @change="changeCheckList">
      <el-checkbox class="greenText" :value="1"><span class="points greenBg"></span>关键词显示 </el-checkbox>
      <el-checkbox class="redText" :value="2"><span class="points redBg"></span>语法问题显示 </el-checkbox>
      <el-checkbox class="blueText" :value="3"><span class="points blueBg"></span>模板格式问题显示 </el-checkbox>
    </el-checkbox-group>
  </div>
  <div class="text-preview" v-loading="state.loading">
    <div class="text-preview-wrap" :class="{ toggleWrap: toggleWrap }" ref="textPreview" @mouseover="onMouseOver" @mouseout="onMouseOut">
      <div class="text-preview-left" ref="textPreviewLeft" @contextmenu.prevent="openContextMenu_left" @click="onClickUnderline">
        <div class="text-content" v-html="obj.left_html" :style="{ '--titleSize': state.titleSize + 'px', '--size': state.size + 'px' }"></div>
        <CustomContextMenu :show="showMenuLeft" :position="{ x: menuX, y: menuY }" :menu-list="menuList" @close="closeContextMenu" @handle-click="handleClick" />
      </div>
      <div class="text-preview-right" ref="textPreviewRight" @contextmenu.prevent="openContextMenu_right" @click="onClickUnderline">
        <div class="text-content" v-html="obj.right_html" :style="{ '--titleSize': state.titleSize + 'px', '--size': state.size + 'px' }"></div>
        <CustomContextMenu :show="showMenuRight" :position="{ x: menuX, y: menuY }" :menu-list="menuList" @close="closeContextMenu" @handle-click="handleClick" />
      </div>
    </div>
    <!-- <div v-if="!state.loading" class="page-tool" :class="{ toggleWrap: toggleWrap }"> -->
    <div class="page-tool" :class="{ toggleWrap: toggleWrap }">
      <div class="page-tool-item" @click="toToggleWrap">
        {{ toggleWrap ? "左右排版" : "上下排版" }}
      </div>
      <div class="page-tool-item active">逐段修改</div>
      <div class="page-tool-item" @click="jumpTo(1)">格式修改</div>
      <!-- <div class="page-tool-item" @click="jumpTo(2)">逐句对照</div> -->
      <div class="page-tool-item" @click="lastPage">上一页</div>
      <div class="page-tool-item" @click="nextPage">下一页</div>
      <div class="page-tool-item">{{ state.pageNum }}/{{ state.numPages }}</div>
      <div class="page-tool-item" @click="pageZoomOut">放大</div>
      <div class="page-tool-item" @click="pageZoomIn">缩小</div>
      <div class="page-tool-item" @click="pageRest">重置缩放</div>
      <div class="page-tool-item" @click="toDownloadFile">下载</div>
    </div>

    <EditForm ref="editForm" :whichPannel="whichPannel" @success="updateText" />

    <PdfDownload ref="pdfDownloadRef" @success="downloadSuccess" />

    <WordDownload ref="wordDownloadRef" :fileName="fileName" :textList="curTextList" @success="downloadSuccess" />
  </div>
</template>
<script lang="ts" setup>
import { fallbackCopyTextToClipboard } from "@/utils/index";
import CustomContextMenu from "@/components/CustomContextMenu/index.vue";
import PdfDownload from "@/components/DownloadTextFile/pdf.vue";
import WordDownload from "@/components/DownloadTextFile/word.vue";
import EditForm from "./EditForm.vue";
import { DICT_TYPE, getDictOptions } from "@/utils/dict";
import download from "@/utils/download";
import { DocTranslateApi } from "@/api/system/translate";
// import { useEmit } from "@/hooks/web/useEmitt";
// import { DocChangeApi } from "@/api/system/change";

const message = useMessage();

const props = defineProps({
  curId: {
    type: String,
    required: true,
  },
  fileName: {
    type: String,
    required: true,
  },
  fileUrl: {
    type: String,
    required: true,
  },
  textList: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(["openHistory"]);

const toggleWrapStorage = JSON.parse(localStorage.getItem("toggleWrapStorage") || "false");
const toggleWrap = ref(toggleWrapStorage);
const toToggleWrap = () => {
  toggleWrap.value = !toggleWrap.value;
  localStorage.setItem("toggleWrapStorage", String(toggleWrap.value));
};

const state = reactive({
  pageNum: 1, //当前页面
  titleSize: 24,
  size: 16,
  numPages: 0, // 总页数
  loading: true, //加载效果
});

const router = useRouter();
const { id } = router.currentRoute.value.query;
const jumpTo = (type: number) => {
  if (type === 1) {
    router.push({ path: "/translate/preview", query: { id } });
  } else if (type === 2) {
    router.push({ path: "/translate/preview/sentence", query: { id } });
  }
};

// 缓存textList
const curTextList: any = ref([]);
// 修改的段落id
const whichId = ref(null);
// 当前修改的左边还是右边的 判断
const whichPannel = ref("right");
const isHover = ref(false);
const isHoverClassName = ref("");
const showMenuLeft = ref(false);
const showMenuRight = ref(false);
const menuX = ref(0);
const menuY = ref(0);

const menuList = [
  { type: 1, menuName: "复制" },
  { type: 2, menuName: "修改" },
];

const onMouseOver = (e: any) => {
  e.preventDefault();
  let curClassName = e.target.className.trim();
  if (curClassName.indexOf("pre_text_") > -1) {
    // 如果curClassName内包含 blueText bold 就去掉
    if (curClassName.indexOf("blueText") > -1 || curClassName.indexOf("bold") > -1 || curClassName.indexOf("dashed-underline") > -1) {
      curClassName = curClassName?.split(" ")[0]?.trim();
    }
    const targetNodes = document.getElementsByClassName(curClassName);
    if (targetNodes && targetNodes.length > 0) {
      isHover.value = true;
      isHoverClassName.value = curClassName;
      // console.log(isHoverClassName.value);
      for (let i = 0; i < targetNodes.length; i++) {
        // 背景色
        targetNodes[i].style.background = "rgba(255,255,0,.8)";
      }
    }
  }
};

const onMouseOut = (e: any) => {
  e.preventDefault();
  let curClassName = e.target.className.trim();
  if (curClassName.indexOf("pre_text_") > -1) {
    // 如果curClassName内包含 blueText bold 就去掉
    if (curClassName.indexOf("blueText") > -1 || curClassName.indexOf("bold") > -1 || curClassName.indexOf("dashed-underline") > -1) {
      curClassName = curClassName?.split(" ")[0]?.trim();
    }
    const targetNodes = document.getElementsByClassName(curClassName);
    if (targetNodes && targetNodes.length > 0) {
      isHover.value = false;
      for (let i = 0; i < targetNodes.length; i++) {
        // 背景色
        targetNodes[i].style.background = "none";
      }
    }
  }
};

const onClickUnderline = (e: any) => {
  if (e.target.className.indexOf("dashed-underline") > -1) {
    // 打开编辑
    emit("openHistory");
  }
};

const openContextMenu_left = (event) => {
  event.preventDefault();
  // 减去左边的宽度
  // menuX.value = event.clientX - 220;
  // menuY.value = event.clientY - 300;
  menuX.value = event.clientX + 20;
  menuY.value = event.clientY + 20;
  showMenuLeft.value = !!isHover.value;
  showMenuRight.value = false;
  whichPannel.value = "left";
  whichId.value = event.target?.dataset?.id;
};

const openContextMenu_right = (event) => {
  event.preventDefault();
  // 减去左边的宽度
  // menuX.value = event.clientX - 160 - (innerWidth - 200) / 2;
  // menuY.value = event.clientY - 300;
  menuX.value = event.clientX + 20;
  menuY.value = event.clientY + 20;

  showMenuLeft.value = false;
  showMenuRight.value = !!isHover.value;
  whichPannel.value = "right";
  whichId.value = event.target?.dataset?.id;
};

const closeContextMenu = () => {
  showMenuLeft.value = false;
  showMenuRight.value = false;
};

const editForm = ref();

// const openEditForm = (id: number) => {
//   editForm.value.openEdit(id);
// };
const updateText = (text) => {
  // 改缓存数据 下载使用
  curTextList.value = curTextList.value.map((s: any) => {
    s.forEach((v: any) => {
      if (String(v.id) === whichId.value) {
        v.flag = 0;
        if (whichPannel.value === "left") {
          v.origin = text;
        } else {
          v.translation = text;
        }
      }
      return v;
    });
    return s;
  });
  // console.log(whichId.value);
  // console.log(curTextList.value);

  // 改html
  let selectedNodes = document.getElementsByClassName(isHoverClassName.value);
  if (whichPannel.value === "left") {
    selectedNodes[0].innerHTML = text;
  } else {
    selectedNodes[1].innerHTML = text;
  }
  // window.location.reload()
};
// 处理点击事件
const handleClick = async (index: number) => {
  let selectedNodes = document.getElementsByClassName(isHoverClassName.value);
  let originText = selectedNodes[0].innerHTML.replace(/<\/?span[^>]*>/g, "").trim();
  let selectedText = selectedNodes[1].innerHTML.replace(/<\/?span[^>]*>/g, "").trim();

  let splitId = selectedNodes[0].getAttribute("data-id");
  if (index === 1) {
    fallbackCopyTextToClipboard(selectedText);
    message.success("文本已复制到剪贴板!");
  } else if (index === 2) {
    editForm.value.open(id, splitId, originText, selectedText);
  }
  closeContextMenu();
};

const pdfDownloadRef = ref();
const wordDownloadRef = ref();
const downloadSuccess = () => {
  state.loading = false;
  message.success("下载成功!");
};

// 下载pdf
async function toDownloadFile() {
  state.loading = true;
  try {
    // if (props.fileName.indexOf(".docx") > -1) {
    //   // wordDownloadRef.value.download();
    //   // const fileUrl = props.fileUrl;
    //   // const fileName = props.fileName;
    //   // const link = document.createElement("a");
    //   // link.href = fileUrl;
    //   // link.download = fileName;
    //   // document.body.appendChild(link);
    //   // link.click();
    //   // document.body.removeChild(link);
    //   const data = await DocTranslateApi.downLoadDoc({ id: props.curId });
    //   download.excel(data, props.fileName.replace(".docx", "（译文）.docx"));
    // } else if (props.fileName.indexOf(".pdf") > -1) {
    //   pdfDownloadRef.value.download();
    // }

    const data = await DocTranslateApi.downLoadDoc({ id: props.curId });
    const ext = props.fileName.split('.').pop()?.toLowerCase();
    let fileName = props.fileName.replace(`.${ext}`, `（译文）.${ext}`);
    download.excel(data, fileName);
  } finally {
    state.loading = false;
  }
}

let obj = ref({
  left_html: "",
  right_html: "",
});

const renderDom = (type: string) => {
  try {
    const list: any = props.textList[state.pageNum - 1];
    obj.value[`${type}_html`] = "";
    for (let i = 0; i < list.length; i++) {
      const item: any = list[i];
      let dashedUnderline = item.flag === 0 ? "dashed-underline" : "";
      const curItemClassName = dashedUnderline ? `pre_text_${i} ${dashedUnderline}` : `pre_text_${i}`;
      obj.value[`${type}_html`] += `<${item.r} class="${curItemClassName}" data-id="${item.id}" style="${item.style}">${type === "left" ? item.origin : item.translation}</${item.r}>`;
    }

    return obj.value[`${type}_html`];
  } catch (error) {
    console.log(error);
    return "";
  }
};

// 关键词 语法 模板
const checkList = ref<number[]>([]);
const keyWords = ref([]);
const keyWordsSelectdList = ref([]);
keyWords.value = getDictOptions(DICT_TYPE.TRANSLATE_KEYWORDS).map((s) => s.label);

const questions_grammar = ref([]);
questions_grammar.value = getDictOptions(DICT_TYPE.TRANSLATE_GRAMMAR).map((s) => s.label);

const questions_template = ref([]);
questions_template.value = getDictOptions(DICT_TYPE.TRANSLATE_TEMPLATE_FORMAT).map((s) => s.label);

const changeCheckList = (data: number[]) => {
  // 关键词显示
  if (data.includes(1)) {
    keyWordsSelectdList.value = keyWords.value;
  } else {
    keyWordsSelectdList.value = [];
  }
  renderDomByWords();
};

// 根据 译文问题 生成dom
const buildWordsDom = (text: string) => {
  let result = text;

  if (checkList.value.includes(2)) {
    const reg = new RegExp(questions_grammar.value.join("|"), "g");
    result = result.replace(reg, function (match) {
      return `<span class="redText bold">${match}</span>`;
    });
  }
  // 正则搜索 keyWordsSelectdList 增加span 标签
  if (keyWordsSelectdList.value.length > 0) {
    const reg = new RegExp(keyWordsSelectdList.value.join("|"), "g");
    result = result.replace(reg, function (match) {
      return `<span class="greenText bold">${match}</span>`;
    });
  }

  return result;
};

const renderDomByWords = () => {
  try {
    const list: any = props.textList[state.pageNum - 1];
    obj.value[`right_html`] = "";
    for (let i = 0; i < list.length; i++) {
      const item: any = list[i];
      // 模板格式问题显示
      let tempQuestionClassname = "";
      if (checkList.value.includes(3)) {
        const reg = new RegExp(questions_template.value.join("|"), "g");
        // 搜索段落是否包含数组内容
        tempQuestionClassname = reg.test(item.translation) ? "blueText bold" : "";
      }
      let dashedUnderline = item.flag === 0 ? "dashed-underline" : "";
      // 生成dom
      // let curItemClassName = tempQuestionClassname ? (dashedUnderline ? `pre_text_${i} ${dashedUnderline} ${tempQuestionClassname}` : `pre_text_${i} ${tempQuestionClassname}`) : `pre_text_${i}`;

      obj.value[`right_html`] += `<${item.r} class="pre_text_${i} ${dashedUnderline} ${tempQuestionClassname}" data-id="${item.id}" style="${item.style}">
    ${buildWordsDom(item.translation)}
    </${item.r}>`;
    }

    return obj.value[`right_html`];
  } catch (error) {
    console.log(error);
    return "";
  }
};

// 历史修改记录
// const historyList = ref([]);
// const getHistoryList = async () => {
//   const { list } = await DocChangeApi.getDocChangePage({
//     transId: props.curId,
//     pageNo: 1,
//     pageSize: 100,
//     originText: "",
//     curText: "",
//   });
//   return list.map((item) => item.splitId) || [];
// };

watch(
  () => props.textList,
  async (newVal) => {
    if (newVal.length === 0) return;
    state.loading = true;
    state.numPages = newVal.length;
    // historyList.value = await getHistoryList();
    obj.value.left_html = await renderDom("left");
    obj.value.right_html = await renderDom("right");
    curTextList.value = newVal;
    nextTick(() => {
      state.loading = false;
    });
  },
  {
    immediate: true,
    deep: true,
  }
);

// 同步滚动
const textPreviewLeft = ref();
const textPreviewRight = ref();
let previousScrollTop = 0;
function syncScroll() {
  // 计算右侧 div 的滚动增量
  const scrollDifference = textPreviewRight.value.scrollTop - previousScrollTop;
  previousScrollTop = textPreviewRight.value.scrollTop;
  // 使左侧 div 滚动相同的距离
  textPreviewLeft.value.scrollTop += scrollDifference;
}
onMounted(() => {
  // console.log(props.textList);
  textPreviewRight.value.addEventListener("scroll", syncScroll);
});

function lastPage() {
  if (state.pageNum > 1) {
    state.pageNum -= 1;
    obj.value.left_html = renderDom("left");
    obj.value.right_html = renderDom("right");
  }
}

function nextPage() {
  if (state.pageNum < state.numPages) {
    state.pageNum += 1;
    obj.value.left_html = renderDom("left");
    obj.value.right_html = renderDom("right");
  }
}

function pageZoomOut() {
  if (state.size < 36) {
    state.titleSize += 1;
    state.size += 1;
  }
}

function pageZoomIn() {
  if (state.size > 11) {
    state.titleSize -= 1;
    state.size -= 1;
  }
}

const pageRest = () => {
  state.titleSize = 24;
  state.size = 16;
};
</script>
<style lang="scss" scoped>
@use "./index.scss" as *;
</style>
