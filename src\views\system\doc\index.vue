<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
      <el-form-item :label="t('docFile.fileName')" prop="fileName">
        <el-input v-model="queryParams.fileName" :placeholder="t('docFile.fileNamePlaceholder')" clearable @keyup.enter="handleQuery" class="!w-200px" />
      </el-form-item>
      <el-form-item :label="t('docFile.aiFileType')" prop="type">
        <el-input v-model="queryParams.type" :placeholder="t('docFile.aiFileTypePlaceholder')" clearable @keyup.enter="handleQuery" class="!w-220px" />
      </el-form-item>
      <el-form-item :label="t('docFile.aiAnalysis')" prop="aiStatus">
        <el-select v-model="queryParams.aiStatus" :placeholder="t('docFile.statusPlaceholder')" clearable class="!w-220px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.AI_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('docFile.createTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :start-placeholder="t('docFile.startDate')"
          :end-placeholder="t('docFile.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          {{ t("common.query") }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          {{ t("common.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('docFile.fileName')" align="center" prop="fileName" />
      <!--      <el-table-column :label="t('docFile.fileUrl')" align="center" prop="fileUrl" />-->
      <el-table-column :label="t('docFile.fileSize')" align="center" prop="fileSize" :formatter="fileSizeFormatter" />
      <el-table-column :label="t('docFile.aiClassificationResult')" align="center" prop="type" />
      <el-table-column :label="t('docFile.aiQualityResult')" align="center" prop="result" />
      <el-table-column :label="t('docFile.reason')" align="center" prop="reason" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-if="scope.row.reason != '' && scope.row.reason != null">
            <div v-for="(res, index) in scope.row.reason.split(',')" :key="index">
              <dict-tag :type="DICT_TYPE.REJECT_REASON" :value="res" />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('docFile.aiAnalysisCount')" align="center" prop="aiNum" />
      <el-table-column :label="t('docFile.aiAnalysis')" align="center" prop="aiStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.AI_STATUS" :value="scope.row.aiStatus" />
        </template>
      </el-table-column>
      <el-table-column :label="t('docFile.approvalStatus')" align="center" prop="status">
        <el-tag type="success">{{ t("docFile.approved") }}</el-tag>
      </el-table-column>
      <el-table-column :label="t('docFile.createTime')" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column :label="t('common.action')" align="center" min-width="80px">
        <template #default="scope">
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['system:doc:delete']">
            {{ t("common.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { dateFormatter } from "@/utils/formatTime";
import { DocFileApi, DocFileVO } from "@/api/system/doc";
import { fileSizeFormatter } from "@/utils";
import { DICT_TYPE, getIntDictOptions } from "@/utils/dict";

/** 文档信息 列表 */
defineOptions({ name: "DocFile" });

const message = useMessage(); // 消息弹窗
const { t } = useI18n(); // 国际化

const loading = ref(true); // 列表的加载中
const list = ref<DocFileVO[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fileName: undefined,
  type: undefined,
  status: 1,
  aiStatus: undefined,
  createTime: [],
});
const queryFormRef = ref(); // 搜索的表单
const exportLoading = ref(false); // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await DocFileApi.getDocFilePage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields();
  queryParams.status = 1;
  handleQuery();
};

/** 添加/修改操作 */
const formRef = ref();
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id);
};

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    await DocFileApi.deleteDocFile(id);
    message.success(t("common.delSuccess"));
    // 刷新列表
    await getList();
  } catch {}
};

/** 初始化 **/
onMounted(() => {
  getList();
});
</script>
