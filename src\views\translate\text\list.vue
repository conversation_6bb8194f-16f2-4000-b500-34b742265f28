<template>
  <ContentWrap>
    <el-form class="-mb-15px" :model="queryParams" ref="historyQueryFormRef" :inline="true" label-width="100px">
      <!-- <el-form-item :label="t('translate.record.original')" prop="origin">
        <el-input v-model="queryParams.origin" :placeholder="t('translate.record.original')" clearable
          @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.record.translate')" prop="translation">
        <el-input v-model="queryParams.translate" :placeholder="t('translate.record.translate')" clearable
          @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item> -->
      <el-form-item :label="t('translate.record.translateScene')" prop="scene">
        <el-select v-model="queryParams.scene" :placeholder="t('translate.record.translateScene')" clearable
          class="!w-240px">
          <el-option v-for="item in getDictOptions(DICT_TYPE.SCENARIO)" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('translate.record.type')" prop="scene">
        <el-select v-model="queryParams.type" :placeholder="t('translate.record.type')" clearable class="!w-240px">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('translate.record.source')" prop="scene">
        <el-select v-model="queryParams.source" :placeholder="t('translate.record.source')" clearable class="!w-240px">
          <el-option v-for="item in getDictOptions(DICT_TYPE.TRANSLATE_SOURCE)" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('common.createTime')" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          :start-placeholder="t('common.startDate')" :end-placeholder="t('common.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.record.creator')" prop="nickname">
        <el-input v-model="queryParams.nickname" :placeholder="t('translate.record.creator')" clearable
          @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-row>
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon icon="ep:search" />{{ t('common.search') }}
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" />{{ t('common.reset') }}
          </el-button>
          <el-button type="success" plain @click="handleExport" :loading="exportLoading"
            v-hasPermi="['system:text-translate-record:export']">
            <Icon icon="ep:download" class="mr-5px" /> {{ t('common.export') }}
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('translate.record.number')" align="center" width="120px">
        <template #default="scope">
          {{ (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.record.original')" align="left" prop="origin" />
      <el-table-column :label="t('translate.record.translate')" align="left" prop="translate" />
      <el-table-column :label="t('translate.record.type')" align="center" prop="type">
        <template #default="scope">
          {{ t(`translate.record.type${scope.row.type}`) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.record.source')" align="center" prop="source">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRANSLATE_SOURCE" :value="scope.row.source" />
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.record.translateScene')" align="center" prop="scene">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SCENARIO" :value="scope.row.scene" />
        </template>
      </el-table-column>
      <el-table-column :label="t('common.creator')" align="center" prop="createName" />
      <el-table-column :label="t('common.createTime')" align="center">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('common.actions')" align="center" width="180px">
        <template #default="scope">
          <el-button link type="primary" @click="handleEdit(scope.row.id)"
            v-hasPermi="['system:text-translate-record:update']"> {{
              t('common.edit') }} </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:text-translate-record:delete']"> {{
              t('common.delete') }} </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 编辑弹窗 -->
    <recordEditForm ref="recordEditFormRef" @success="getList" />
  </ContentWrap>
</template>
<script lang="ts" name="RecordTranslate" setup>
import { DocTranslateApi } from "@/api/system/translate";
import { ElMessageBox } from "element-plus";
import type { Action } from "element-plus";
import { formatDate } from "@/utils/formatTime";
import { useEmit } from "@/hooks/web/useEmitt";
import { DICT_TYPE, getDictOptions } from "@/utils/dict";
import download from "@/utils/download";
import { useI18n } from 'vue-i18n';
import recordEditForm from "./recordEditForm.vue";

defineOptions({ name: "RecordTranslate" });

const { t } = useI18n();
const router = useRouter();
const message = useMessage(); // 消息弹窗
const loading = ref(true); // 列表的加载中
const list = ref<any[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  scene: "",
  nickname: "",
  // origin: "",
  // translate: "",
  type: "", // type  1：中翻英  2：英翻中
  source: "", // 1：本系统   2：argus系统
  createTime: [],
});
const historyQueryFormRef = ref();
const recordEditFormRef = ref();
const exportLoading = ref(false); // 导出的加载中


const typeOptions = [
  { label: t('translate.record.type1'), value: 1 },
  { label: t('translate.record.type2'), value: 2 }
];

const sourceOptions = [
  { label: t('translate.record.source1'), value: 1 },
  { label: t('translate.record.source2'), value: 2 }
]

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await DocTranslateApi.getRecordTranslatePage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  historyQueryFormRef.value?.resetFields();
  handleQuery();
};

onMounted(() => {
  getList();
});

// 打开详情编辑
const handleEdit = (id: number) => {
  recordEditFormRef.value.open(id);
};

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    await DocTranslateApi.deleteRecordTranslate(id);
    message.success(t('translate.record.deleteSuccess'));
    // 刷新列表
    await getList();
  } catch { }
};

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm();
    // 发起导出
    exportLoading.value = true;
    const data = await DocTranslateApi.exportRecordTranslate(queryParams);
    download.excel(data, t('translate.record.exportFile'));
  } catch {
  } finally {
    exportLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
.question-table {
  overflow: hidden;
}

::v-deep(.el-popper) {
  max-width: 300px !important;
}
</style>
