import request from '@/config/axios'

// 翻译历史版本信息 VO
export interface TranslateHistoryVersionVO {
  id: number // 主键ID
  transId: number // 文档翻译ID
  fileName: string // 文件名称
  origin: string // 原文链接
  translate: string // 当前译文地址
  isStudy: string // 是否学习
  version: number // 当前版本
  source: number // 来源：1：文档翻译；2：人工上传
}

// 翻译历史版本信息 API
export const TranslateHistoryVersionApi = {
  // 查询翻译历史版本信息分页
  getTranslateHistoryVersionPage: async (params: any) => {
    return await request.get({ url: `/system/trans/history/page`, params })
  },

  // 查询翻译历史版本信息详情
  getTranslateHistoryVersion: async (params: any) => {
    return await request.get({ url: `/system/trans/history/get`, params })
  },

  // 新增翻译历史版本信息
  createTranslateHistoryVersion: async (data: TranslateHistoryVersionVO) => {
    return await request.post({ url: `/system/trans/history/create`, data })
  },

  // 修改翻译历史版本信息
  updateTranslateHistoryVersion: async (data: TranslateHistoryVersionVO) => {
    return await request.put({ url: `/system/trans/history/update`, data })
  },

  // 删除翻译历史版本信息
  deleteTranslateHistoryVersion: async (id: number) => {
    return await request.delete({ url: `/system/trans/history/delete?id=` + id })
  },

  // 导出翻译历史版本信息 Excel
  exportTranslateHistoryVersion: async (params) => {
    return await request.download({ url: `/system/trans/history/export-excel`, params })
  },
}
