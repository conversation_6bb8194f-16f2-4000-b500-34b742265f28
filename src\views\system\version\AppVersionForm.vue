<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" v-loading="formLoading">
      <el-form-item label="类型" prop="appType">
        <el-select v-model="formData.appType" placeholder="请选择类型">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.APP_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        <el-input v-model="formData.version" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="版本编号" prop="versionCode">
        <el-input-number v-model="formData.versionCode" placeholder="请输入版本编号" class="!w-240px" />
      </el-form-item>
      <!-- <el-form-item label="APK文件" prop="apkUrl">
        <UploadFile v-model="apkUrl" :file-type="['apk']" :file-size="200" :limit="1" />
      </el-form-item> -->
      <el-form-item label="更新内容" prop="updateContent">
        <el-input type="textarea" v-model="formData.updateContent" :rows="5" />
      </el-form-item>
      <!-- <el-form-item label="是否强制更新" prop="isForce">
        <el-select v-model="formData.isForce" placeholder="请选择是否强制更新">
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item> -->
     
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import * as AppVersionApi from '@/api/system/version'
import type { UploadUserFile } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const apkUrl = ref<UploadUserFile[]>([]) // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  version: undefined,
  versionCode: undefined,
  apkUrl: '',
  apkName: '',
  updateContent: '',
  isForce: undefined,
  appType: undefined,
  size: 0
})
const formRules = reactive({
  version: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
  updateContent: [{ required: true, message: '更新内容不能为空', trigger: 'blur' }],
  isForce: [{ required: true, message: '是否强制更新不能为空', trigger: 'blur' }],
  appType: [{ required: true, message: '类型不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AppVersionApi.getAppVersion(id)
      apkUrl.value[0] = {
        url: formData.value.apkUrl,
        size: formData.value.size,
        name: formData.value.apkName
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
watch(
  apkUrl,
  newUrl => {
    formData.value.apkUrl = newUrl == null ? '' : newUrl[0].url
    formData.value.size = newUrl == null ? '' : newUrl[0].size
    formData.value.apkName = newUrl == null ? '' : newUrl[0].name
  },
  { deep: true }
)
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AppVersionApi.AppVersionVO
    if (formType.value === 'create') {
      await AppVersionApi.createAppVersion(data)
      message.success(t('common.createSuccess'))
    } else {
      await AppVersionApi.updateAppVersion(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    version: undefined,
    versionCode: undefined,
    apkUrl: undefined,
    updateContent: undefined,
    isForce: undefined,
    appType: undefined,
    size: undefined
  }
  formRef.value?.resetFields()
}
</script>
