<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="机构名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入机构名称" />
      </el-form-item>
      <el-form-item label="机构地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入机构地址" />
      </el-form-item>
      <el-form-item label="机构代码证" prop="codeNumber">
        <el-input v-model="formData.codeNumber" placeholder="请输入机构代码证" />
      </el-form-item>
      <el-form-item label="法定代表人" prop="contactUser">
        <el-input v-model="formData.contactUser" placeholder="请输入法定代表人" />
      </el-form-item>
      <el-form-item label="联系方式" prop="contactPhoneNumber">
        <el-input v-model="formData.contactPhoneNumber" placeholder="请输入联系方式" />
      </el-form-item>
      <el-form-item label="员工总数" prop="employeeNumber">
        <el-input v-model="formData.employeeNumber" placeholder="请输入员工总数" />
      </el-form-item>
      <el-form-item label="设备总数" prop="deviceNumber">
        <el-input v-model="formData.deviceNumber" placeholder="请输入设备总数" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { OrgServiceApi, OrgServiceVO } from '@/api/system/orgservice'

/** 维保机构 表单 */
defineOptions({ name: 'OrgServiceForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  address: undefined,
  codeNumber: undefined,
  contactUser: undefined,
  contactPhoneNumber: undefined,
  employeeNumber: undefined,
  deviceNumber: undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrgServiceApi.getOrgService(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrgServiceVO
    if (formType.value === 'create') {
      await OrgServiceApi.createOrgService(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrgServiceApi.updateOrgService(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    address: undefined,
    codeNumber: undefined,
    contactUser: undefined,
    contactPhoneNumber: undefined,
    employeeNumber: undefined,
    deviceNumber: undefined,
  }
  formRef.value?.resetFields()
}
</script>