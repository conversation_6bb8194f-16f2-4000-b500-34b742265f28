import request from "@/config/axios";

// 文档修改信息 VO
export interface DocChangeVO {
  id?: number | string; // 主键ID
  splitId: number | string; // 分段ID
  origin: string; // 原文
  translation: string; // 译文
  originChange?: string; // 原文修改
  translationChange?: string; // 译文修改
  remark?: string; // 备注
  status?: number; // 审核状态: 0:待审核; 1:已通过; 2:未通过
}

// 文档修改信息 API
export const DocChangeApi = {
  // 查询文档修改信息分页
  getDocChangePage: async (params: any) => {
    return await request.get({ url: `/system/change/page`, params });
  },

  // 查询文档修改信息详情
  getDocChange: async (id: number) => {
    return await request.get({ url: `/system/change/get?id=` + id });
  },

  // 新增文档修改信息
  createDocChange: async (data: DocChangeVO) => {
    return await request.post({ url: `/system/change/create`, data });
  },

  // 修改文档修改信息
  updateDocChange: async (data: DocChangeVO) => {
    return await request.put({ url: `/system/change/update`, data });
  },

  // 删除文档修改信息
  deleteDocChange: async (id: number) => {
    return await request.delete({ url: `/system/change/delete?id=` + id });
  },

  // 审核文档
  auditDocChange: async (data) => {
    return await request.put({ url: `/system/change/audit`, data });
  },

  // 导出文档
  exportDocChange: async (params) => {
    return await request.download({ url: `/system/change/export`, params });
  },
};
