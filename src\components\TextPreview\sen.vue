<template>
  <div class="text-preview" v-loading="state.loading">
    <div class="text-preview-wrap" :class="{ 'toggleWrap': toggleWrap }" ref="textPreview" @mouseover="onMouseOver" @mouseout="onMouseOut">
      <div class="text-preview-left">
        <div class="text-content" v-html="obj.left_html" :style="{ '--titleSize': state.titleSize + 'px', '--size': state.size + 'px' }"></div>
      </div>
      <div class="text-preview-right" @contextmenu.prevent="openContextMenu">
        <div class="text-content" v-html="obj.right_html" :style="{ '--titleSize': state.titleSize + 'px', '--size': state.size + 'px' }"></div>
        <CustomContextMenu :show="showMenu" :position="{ x: menuX, y: menuY }" :menu-list="menuList" @close="closeContextMenu" @handle-click="handleClick" />
      </div>
    </div>
    <div v-if="!state.loading" class="page-tool" :class="{ 'toggleWrap': toggleWrap }">
      <div class="page-tool-item" @click="toToggleWrap">{{ toggleWrap ? "左右排版" : "上下排版" }}</div>
      <div class="page-tool-item" @click="jumpTo(2)">逐段修改</div>
      <div class="page-tool-item" @click="jumpTo(1)">格式修改</div>
      <!-- <div class="page-tool-item active">逐句对照</div> -->
      <div class="page-tool-item" @click="lastPage">上一页</div>
      <div class="page-tool-item" @click="nextPage">下一页</div>
      <div class="page-tool-item">{{ state.pageNum }}/{{ state.numPages }}</div>
      <div class="page-tool-item" @click="pageZoomOut">放大</div>
      <div class="page-tool-item" @click="pageZoomIn">缩小</div>
      <div class="page-tool-item" @click="pageRest">重置缩放</div>
      <div class="page-tool-item" @click="toDownloadPDF">下载</div>
    </div>

    <EditForm ref="editForm" @success="updateText" />

    <PdfDownload ref="pdfDownloadRef" @success="downloadSuccess" />
  </div>
</template>
<script lang="ts" setup>
import { fallbackCopyTextToClipboard } from "@/utils/index";
import CustomContextMenu from "@/components/CustomContextMenu/index.vue";
import PdfDownload from "@/components/DownloadTextFile/pdf.vue";
import EditForm from "./EditForm.vue";

const message = useMessage();

const props = defineProps({
  textList: {
    type: Array,
    required: true,
  },
});

const toggleWrapStorage = JSON.parse(localStorage.getItem("toggleWrapStorage") || "false");
const toggleWrap = ref(toggleWrapStorage);
const toToggleWrap = () => {
  toggleWrap.value = !toggleWrap.value;
  localStorage.setItem("toggleWrapStorage", String(toggleWrap.value));
};

const state = reactive({
  pageNum: 1, //当前页面
  titleSize: 24,
  size: 16,
  numPages: 0, // 总页数
  loading: true, //加载效果
});

const router = useRouter();
const { id } = router.currentRoute.value.query;
const jumpTo = (type: number) => {
  if (type === 1) {
    router.push({ path: "/translate/preview", query: { id } });
  } else if (type === 2) {
    router.push({ path: "/translate/preview/text", query: { id } });
  }
};

const isHover = ref(false);
const isHoverClassName = ref("");
const showMenu = ref(false);
const menuX = ref(0);
const menuY = ref(0);

const menuList = [
  { type: 1, menuName: "复制" },
  { type: 2, menuName: "修改" },
];

const onMouseOver = (e: any) => {
  if (e.target.className.indexOf("pre_text_") > -1) {
    const targetNodes = document.getElementsByClassName(e.target.className);
    if (targetNodes && targetNodes.length > 0) {
      isHover.value = true;
      isHoverClassName.value = e.target.className;
      for (let i = 0; i < targetNodes.length; i++) {
        targetNodes[i].style.background = "rgba(255,255,0,.8)";
      }
    }
  }
};

const onMouseOut = (e: any) => {
  if (e.target.className.indexOf("pre_text_") > -1) {
    const targetNodes = document.getElementsByClassName(e.target.className);
    if (targetNodes && targetNodes.length > 0) {
      isHover.value = false;
      for (let i = 0; i < targetNodes.length; i++) {
        targetNodes[i].style.background = "none";
      }
    }
  }
};

const openContextMenu = (event) => {
  event.preventDefault();
  // 减去左边的宽度
  menuX.value = event.clientX - 180 - (innerWidth - 200) / 2;
  menuY.value = event.clientY - 145;
  showMenu.value = !!isHover.value;
};

const closeContextMenu = () => {
  showMenu.value = false;
};

const editForm = ref();
const updateText = (text) => {
  let selectedNodes = document.getElementsByClassName(isHoverClassName.value);
  selectedNodes[1].innerHTML = text;
  // window.location.reload()
};
const handleClick = async (index: number) => {
  let selectedNodes = document.getElementsByClassName(isHoverClassName.value);
  let originText = selectedNodes[0].innerHTML;
  let selectedText = selectedNodes[1].innerHTML;
  if (index === 1) {
    fallbackCopyTextToClipboard(selectedText);
    message.success("文本已复制到剪贴板!");
  } else if (index === 2) {
    let id: string = props.textList[0]?.id || "";
    editForm.value.open(id, originText, selectedText);
  }
  closeContextMenu();
};

const pdfDownloadRef = ref();
const downloadSuccess = () => {
  state.loading = false;
  message.success("下载成功!");
};
// // 下载pdf
function toDownloadPDF() {
  state.loading = true;
   pdfDownloadRef.value.download(id);
}

let obj = ref({
  left_html: "",
  right_html: "",
});

const renderDom = (type: string) => {
  const list: any = props.textList[state.pageNum - 1];
  obj.value[`${type}_html`] = "";
  for (let i = 0; i < list.length; i++) {
    const item: any = list[i];
    obj.value[`${type}_html`] += `<${item.r} style="${item.style}">`;
    const contrastList: any = item.contrastList;
    for (let j = 0; j < contrastList.length; j++) {
      obj.value[`${type}_html`] += `<span class="pre_text_${i}_${j}">${type === "left" ? contrastList[j].origin : contrastList[j].translation}</span>`;
    }

    obj.value[`${type}_html`] += `</${item.r}>`;
  }

  return obj.value[`${type}_html`];
};

watch(
  () => props.textList,
  async (newVal) => {
    state.loading = true;
    state.numPages = newVal.length;
    obj.value.left_html = await renderDom("left");
    obj.value.right_html = await renderDom("right");
    nextTick(() => {
      state.loading = false;
    });
  }
);

onMounted(() => {});

function lastPage() {
  if (state.pageNum > 1) {
    state.pageNum -= 1;
    obj.value.left_html = renderDom("left");
    obj.value.right_html = renderDom("right");
  }
}

function nextPage() {
  if (state.pageNum < state.numPages) {
    state.pageNum += 1;
    obj.value.left_html = renderDom("left");
    obj.value.right_html = renderDom("right");
  }
}

function pageZoomOut() {
  if (state.size < 36) {
    state.titleSize += 1;
    state.size += 1;
  }
}

function pageZoomIn() {
  if (state.size > 11) {
    state.titleSize -= 1;
    state.size -= 1;
  }
}

const pageRest = () => {
  state.titleSize = 24;
  state.size = 16;
};
</script>
<style lang="scss" scoped>
@use "./index.scss" as *;
</style>
