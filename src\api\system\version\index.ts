import request from '@/config/axios'

export interface AppVersionVO {
  id: number
  version: string
  versionCode: number
  apkUrl: string
  updateContent: string
  isForce: number
  appType: number
  size: string
}

// 查询版本管理列表
export const getAppVersionPage = async (params) => {
  return await request.get({ url: `/system/version/page`, params })
}

// 查询版本管理详情
export const getAppVersion = async (id: number) => {
  return await request.get({ url: `/system/version/get?id=` + id })
}

// 新增版本管理
export const createAppVersion = async (data: AppVersionVO) => {
  return await request.post({ url: `/system/version/create`, data })
}

// 修改版本管理
export const updateAppVersion = async (data: AppVersionVO) => {
  return await request.put({ url: `/system/version/update`, data })
}

// 删除版本管理
export const deleteAppVersion = async (id: number) => {
  return await request.delete({ url: `/system/version/delete?id=` + id })
}

// 导出版本管理 Excel
export const exportAppVersion = async (params) => {
  return await request.download({ url: `/system/version/export-excel`, params })
}

// 获取最后一个版本更新内容   status 为null 或者1 弹窗  为0 不弹窗
// appType 1
export const findLastVersion = async () => {
  return await request.post({ url: `/system/version/findLastVersion`, data: { appType: 1 } })
}

// 录入已弹窗的用户信息 versionId
export const readAppVersion = async (data: { versionId: string }) => {
  return await request.post({ url: `/system/version/pop`, data })
}