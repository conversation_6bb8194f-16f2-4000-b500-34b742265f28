import html2pdf from 'html2pdf.js';

interface GeneratePDFOptions {
  filename?: string;
  margin?: number;
  imageQuality?: number;
  scale?: number;
  orientation?: 'portrait' | 'landscape';
}

export function generatePDF({
  filename = 'document.pdf',
  margin = 10, // 英寸
  imageQuality = 0.98,
  scale = 1,
  orientation = 'portrait',
}: GeneratePDFOptions = {}): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      const element = document.querySelector('#pdfDom') as HTMLElement;
      if (!element) {
        reject(new Error('无法找到指定的元素'));
        return;
      }

      const options = {
        margin: margin,
        filename: filename,
        image: { type: 'jpeg', quality: imageQuality },
        html2canvas: { scale: scale, useCORS: true },
        jsPDF: { unit: 'mm', format: 'a4', orientation: orientation }
      };

      html2pdf().from(element).set(options).save().then(() => {
        resolve(); // PDF 生成成功，通知完成
      }).catch((error) => {
        reject(new Error('PDF生成失败: ' + error.message));
      });
    } catch (error) {
      reject(new Error('PDF生成失败: ' + error.message));
    }
  });
}
