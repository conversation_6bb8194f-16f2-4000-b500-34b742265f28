<template>
  <div class="audit-detail">
    <el-space v-if="loading" direction="horizontal" wrap fill :fill-ratio="40" style="width: 100%">
      <div class="space-item">
        <el-skeleton animated :rows="20" :count="1" />
      </div>
      <div class="space-item space-item2">
        <el-skeleton animated :rows="20" :count="1" />
      </div>
    </el-space>
    <div class="showDetail" v-else>
      <div class="pdf-box">
        <PdfPreviewSingle :pdfUrl="pdfUrl" :pdfName="pdfName" />
      </div>
      <div class="audit-box">
        <div class="audit-title">{{ t('auditDetail.fileApproval') }}</div>
        <div class="audit-content">
          <el-tabs v-model="activeName" class="title-tabs" @tab-click="handleClick">
            <el-tab-pane :label="t('auditDetail.fileAttributes')" name="first">
              <div class="file-attr">
                <div class="info">
                  <p class="info-title">{{ t('auditDetail.fileName') }}：</p>
                  <p class="info-content pointer">
                    {{ info.fileName }}
                    <Icon @click="toChangeFileName" :size="24" icon="ep:edit" />
                  </p>
                </div>
                <div class="info">
                  <p class="info-title">
                    {{ t('auditDetail.folderLocation') }}：
                    <el-popover :visible="visibleAi" placement="right" :width="350" trigger="click">
                      <template #reference>
                        <el-button class="icon ai-btn" @click="visibleAi = true">AI</el-button>
                      </template>
                      <p class="pop-title">{{ t('auditDetail.aiClassificationTip') }}</p>
                      <div class="pop-content">
                        <el-radio-group v-model="folderId">
                          <el-radio v-for="(item, index) in arr" :key="index" :value="index" size="large">{{ item }} </el-radio>
                        </el-radio-group>
                        <div class="submit-box">
                          <el-button size="small" text @click="visibleAi = false">{{ t('common.cancel') }}</el-button>
                          <el-button size="small" type="primary" @click="submitFolderPath">{{ t('common.confirm') }} </el-button>
                        </div>
                      </div>
                    </el-popover>
                  </p>
                  <p class="info-content">{{ folderPath }}</p>
                </div>
                <div class="info">
                  <p class="info-title">{{ t('auditDetail.approvalProcess') }}：</p>
                  <p class="info-content">{{ t('auditDetail.demoApprovalProcess') }}</p>
                </div>
                <div class="info">
                  <p class="info-title">{{ t('auditDetail.namingRules') }}：</p>
                  <p class="info-content">{{ t('auditDetail.chineseNamingPrinciples') }}</p>
                </div>
                <div class="info">
                  <p class="info-title">{{ t('auditDetail.fileVersion') }}：</p>
                  <p class="info-content">1.0.0</p>
                </div>
                <div class="info">
                  <p class="info-title">{{ t('auditDetail.schemeNumber') }}：</p>
                  <p class="info-content">FA000020240820000001</p>
                </div>
                <div class="info">
                  <p class="info-title">{{ t('auditDetail.projectName') }}：</p>
                  <p class="info-content">{{ t('auditDetail.demoProject') }}</p>
                </div>
                <div class="info">
                  <p class="info-title">{{ t('auditDetail.countryName') }}：</p>
                  <p class="info-content">{{ t('auditDetail.china') }}</p>
                </div>
                <div class="submit-box right">
                  <el-button type="primary" @click="submitForm">{{ t('auditDetail.filePlan') }}</el-button>
                  <el-button type="primary" @click="submitForm">{{ t('common.edit') }}</el-button>
                  <el-button type="primary" @click="submitForm">{{ t('auditDetail.move') }}</el-button>
                  <el-button type="primary" @click="handleAudit(info.id)">{{ t('auditDetail.approve') }}</el-button>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane :label="t('auditDetail.approvalOpinion')" name="second">
              <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="100px">
                <el-form-item class="info" label=" " prop="status" label-width="25px">
                  <p class="info-title">
                    {{ t('auditDetail.approvalResult') }}：
                    <el-popover :visible="visibleCheckAi" placement="right" :width="500" trigger="click">
                      <template #reference>
                        <el-button class="icon ai-btn" @click="visibleCheckAi = true">AI</el-button>
                      </template>
                      <p class="pop-title">{{ t('auditDetail.aiQualityTip') }}</p>
                      <div class="pop-content">
                        <el-form-item class="info" :label="t('auditDetail.qualityResult')" label-width="100px">
                          <el-radio-group v-model="checkAiId">
                            <el-radio :value="1" size="large">{{ t('auditDetail.approvalPassed') }}</el-radio>
                            <el-radio :value="2" size="large">{{ t('auditDetail.approvalRejected') }}</el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="checkAiId == 2" class="info" :label="t('auditDetail.reason')" label-width="100px">
                          <el-select v-model="reasonList" clearable multiple :placeholder="t('auditDetail.selectRejectReason')" style="width: 80%">
                            <el-option v-for="dict in getStrDictOptions(DICT_TYPE.REJECT_REASON)" :key="dict.value" :label="dict.label" :value="dict.value" />
                          </el-select>
                        </el-form-item>
                        <div class="submit-box">
                          <el-button size="small" text @click="visibleCheckAi = false">{{ t('common.cancel') }} </el-button>
                          <el-button size="small" type="primary" @click="submitCheck">{{ t('common.confirm') }} </el-button>
                        </div>
                      </div>
                    </el-popover>
                  </p>
                  <p class="info-content">
                    <el-radio-group v-model="formData.status">
                      <p class="radio-item">
                        <el-radio :value="1" size="large">{{ t('auditDetail.approvalPassed') }}</el-radio>
                      </p>
                      <p class="radio-item">
                        <el-radio :value="2" size="large">{{ t('auditDetail.approvalRejected') }}</el-radio>
                      </p>
                    </el-radio-group>
                  </p>
                </el-form-item>
                <el-form-item v-if="formData.status == 2" class="info" label=" " prop="reasonList" label-width="25px">
                  <p class="info-title">{{ t('auditDetail.rejectReason') }}：</p>
                  <p class="info-content">
                    <el-select v-model="formData.reasonList" clearable multiple :placeholder="t('auditDetail.selectRejectReason')" style="width: 80%">
                      <el-option v-for="dict in getStrDictOptions(DICT_TYPE.REJECT_REASON)" :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </p>
                </el-form-item>
                <el-form-item class="info" label=" " prop="name" label-width="25px">
                  <p class="info-title">{{ t('auditDetail.namingRules') }}：</p>
                  <p class="info-content">{{ t('auditDetail.chineseNamingPrinciples') }}</p>
                </el-form-item>
                <el-form-item class="info" label=" " prop="username" label-width="25px">
                  <p class="info-title">{{ t('auditDetail.responsible') }}：</p>
                  <p class="info-content">
                    <span>{{ userName }}</span>
                  </p>
                </el-form-item>
                <div class="submit-box">
                  <el-button type="primary" @click="submitAudit">{{ t('common.confirm') }}</el-button>
                </div>
              </el-form>
            </el-tab-pane>
            <!--            <el-tab-pane label="质疑列表" name="third">-->
            <!--              <div class="info">-->
            <!--                <QuestionTable :id="curId" />-->
            <!--              </div>-->
            <!--            </el-tab-pane>-->
            <!--            <el-tab-pane label="文件关联" name="fourth">文件关联</el-tab-pane>-->
            <!--            <el-tab-pane label="评论" name="fifth">评论</el-tab-pane>-->
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DocFileApi, DocFileVO } from "@/api/system/doc";
import PdfPreviewSingle from "@/components/PdfPreview/single.vue";
import type { TabsPaneContext } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import QuestionTable from "./QuestionTable.vue";
import { DICT_TYPE, getStrDictOptions } from "@/utils/dict";
import { useUserStore } from "@/store/modules/user";

defineOptions({ name: "SystemDocAuditDetail" });
const formLoading = ref(false); // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const loading = ref(true);
const info: any = ref({});
const router = useRouter();
const curId: any = router.currentRoute.value.query.id || "";

const message = useMessage(); // 消息弹窗
const { t } = useI18n(); // 国际化
const userStore = useUserStore();
const userName = computed(() => userStore.user.nickname ?? "Admin");

const pdfUrl = ref("");
const pdfName = ref("");
const formRules = reactive({
  status: [{ required: true, message: "审批结果不能为空", trigger: "blur" }],
  reasonList: [{ required: true, message: "驳回原因不能为空", trigger: "blur" }],
});
const formData: any = ref({
  id: 0,
  status: null,
  reasonList: [],
});
const activeName = ref("first");

// const currentDate = new Date().toDateString()

const visibleAi = ref(false);
const folderId = ref(0);
const folderPath = ref("");
const arr = ref([]);
const submitFolderPath = () => {
  visibleAi.value = false;
  folderPath.value = arr.value[folderId.value];
};

const visibleCheckAi = ref(false);
const checkAiId = ref();
const reasonList = ref([]);
const submitCheck = () => {
  visibleCheckAi.value = false;
  formData.value.status = checkAiId.value;
  formData.value.reasonList = reasonList.value;
};

const fetchData = async () => {
  info.value = await DocFileApi.getDocFile(curId);
  formData.value.id = info.value.id;
  formData.value.reasonList = [];
  loading.value = false;
  arr.value = info.value.type?.split(",") || "";
  // 开发环境 TODO: 本地测试使用
  if (import.meta.env.DEV) {
    if (info.value.fileUrl) {
      info.value.fileUrl = info.value.fileUrl.replace(import.meta.env.VITE_APP_PROXY_FILES_URL, "/file-api");
    }
  }
  // pdf 文件地址
  pdfUrl.value = info.value.fileUrl;

  if (info.value.result == "Pass") {
    checkAiId.value = 1;
  } else {
    checkAiId.value = 2;
    reasonList.value = info.value.reason?.split(",") || "";
  }
  pdfName.value = info.value.fileName;
};

const handleAudit = (id: number) => {
  activeName.value = "second";
};

const backAuditList = () => {
  router.push({
    path: "audit",
  });
};

const handleClick = (tab: TabsPaneContext, event: Event) => {
  // console.log(tab, event);
};

const limitFileType = [".pdf", ".docx", ".xlsx"];
const toChangeFileName = () => {
  ElMessageBox.prompt("请输入新的文件名称", "提示", {
    confirmButtonText: "确认修改",
    cancelButtonText: "取消",
    inputValidator: (value: string) => {
      if (limitFileType.some((fileType) => value.endsWith(fileType))) {
        if (value.trim().split(".")[0].length > 0) return true;
        return false;
      } else {
        return `请输入包含 ${limitFileType.join(",")} 后缀的文件名称`;
      }
    },
    inputErrorMessage: "请输入正确的文件名称",
  })
    .then(({ value }) => {
      ElMessage({ type: "success", message: `修改成功` });
      info.value.fileName = value;
    })
    .catch(() => {
      console.log("Cancel");
    });
};
const submitForm = () => {};

const formRef = ref();
const submitAudit = async () => {
  // 校验表单
  if (!formRef.value) return;
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  formLoading.value = true;
  try {
    const data = (formData.value as unknown) as DocFileVO;
    if (data.status == 1) {
      data.reasonList = [];
    }
    await DocFileApi.updateDocFile(data);
    message.success("审核成功");
    backAuditList();
  } finally {
    formLoading.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.pop-title {
  margin: 10px 0;
  text-align: center;
  font-weight: 700;
}

.pop-content {
  width: 100%;
  height: 100%;
  padding: 0 20px;
  background: #fff;

  .submit-box {
    margin: 20px 0 10px 0;
    text-align: center;
  }
}

.audit-detail {
  width: 100%;
  min-height: 800px;
  color: #1d2129;
  background: #fff;
  border: 1px solid #f2f3f5;
  box-shadow: 0 4px 10px rgb(35 46 67 / 6%);
  overflow: hidden;

  .space-item {
    width: 50%;
    padding: 0 20px 0 0;
    border-right: 1px solid #f2f3f5;

    &.space-item2 {
      border: none;
      padding: 0 0 0 10px;
    }
  }

  .showDetail {
    display: flex;

    .pdf-box {
      flex: 0 0 700px;
    }

    .audit-box {
      flex: 1;
      width: 100%;
      overflow: auto;

      .audit-title {
        width: 100%;
        height: 50px;
        line-height: 50px;
        background: #000;
        font-size: 20px;
        padding-left: 20px;
        font-weight: 700;
        color: #fff;
      }

      .audit-content {
        padding: 0 20px;
        width: 100%;
      }

      .info {
        margin-bottom: 20px;
        width: 100%;

        .info-title {
          font-size: 18px;
          font-weight: 700;
          color: #333;
          margin-bottom: 10px;
        }

        .info-content {
          font-size: 14px;
          color: #606266;
          width: 100%;
          line-height: 24px;
          display: flex;
          align-items: center;
          word-break: break-all;
        }

        // ::v-deep(.el-icon) {
        //   display: inline-block;
        //   margin-left: 10px;
        //   cursor: pointer;
        //   width: 24px;
        //   height: 24px;

        //   &:hover {
        //     color: #1989fa;
        //   }

        //   span {
        //     display: inline-block;
        //     font-size: 24px !important;
        //     font-weight: 700;
        //   }
        // }

        ::v-deep(.ai-btn) {
          display: inline-block;
          cursor: pointer;
          width: 30px;
          height: 30px;
          line-height: 24px;
          text-align: center;
          border: 3px solid #1989fa;
          color: #1989fa;
          border-radius: 4px;
          font-weight: 700;
          font-size: 14px;
          box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
          margin-left: 10px;
          box-sizing: border-box;
          padding: 0;

          &:hover {
            background: #1989fa;
            color: #fff;
          }

          span {
            display: block;
            width: 24px;
            height: 24px;
            line-height: 24px;
          }
        }
      }
    }
  }

  .el-radio-group {
    display: block;
    padding-left: 20px;
  }

  .submit-box {
    margin-top: 20px;
    margin-left: 50px;
    padding: 30px 0;

    &.right {
      text-align: right;
    }
  }
}
</style>
