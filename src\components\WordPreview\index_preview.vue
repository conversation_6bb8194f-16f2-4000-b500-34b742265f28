<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" @toggle-full="toggleFull" width="90%" top="100px">
    <div class="word-preview" v-loading="state.loading">
      <div class="word-flex-box" :class="toggleWrap && 'toggleWrap'" v-if="!state.loading">
        <div class="word-flex-wrap">
          <div class="word-wrap" :style="{
            width: `${docWidth}`,
            height: `${docHeight}`,
          }">
            <DocumentEditor id="source" ref="wordPreviewSingleLeft" :documentServerUrl="VITE_ONLY_OFFICE_BASE_URL"
              :config="sourceConfig" :events_onDocumentReady="onDocumentReadySource" />
          </div>
        </div>
        <div class="word-flex-wrap">
          <div class="word-wrap" :style="{
            width: `${docWidth}`,
            height: `${docHeight}`,
          }">
            <DocumentEditor id="target" ref="wordPreviewSingleRight" :documentServerUrl="VITE_ONLY_OFFICE_BASE_URL"
              :config="targetConfig" :events_onDocumentReady="onDocumentReadyTarget" />
          </div>
        </div>
      </div>
      <div class="page-tool" :style="`bottom: ${isFullscreen ? '-30px' : '-25px'}`">
        <div class="page-tool-item active">{{ t('wordPreview.formatEdit') }}</div>
        <div class="page-tool-item" @click="toToggleWrap">
          {{ toggleWrap ? t('wordPreview.leftRightLayout') : t('wordPreview.topBottomLayout') }}
        </div>
        <div class="page-tool-item" @click="toggleComparison">{{ isComparison ? t('wordPreview.closeComparison') :
          t('wordPreview.openComparison') }}</div>
        <div class="page-tool-item" @click="downloadWord('origin')">{{ t('wordPreview.downloadOriginal') }}</div>
        <div class="page-tool-item" @click="downloadWord('source')">{{ t('wordPreview.downloadTranslation') }}</div>
      </div>
    </div>
  </Dialog>
</template>
<script lang="ts" setup>
import { getUserProfile } from "@/api/system/user/profile";
import { DocumentEditor } from "@onlyoffice/document-editor-vue";
import { debounce } from "@/utils/index";
import imgLogo from "@/assets/imgs/logo.png";
import { ElMessage, ElMessageBox } from "element-plus";
import { checkFileType } from "@/utils/check-file-type";
import { useRoute } from "vue-router";
import { computed } from "vue";
import { i18n } from "@/plugins/vueI18n";
import { useI18n } from "vue-i18n";

const state = reactive({
  loading: true, //加载
  id: '',
  scale: 1,
  fileName: '',
  source_origin: "", // 预览p文件地址
  source_translate: "", // 预览文件地址
  documentType_origin: "",
  fileType_origin: "",
  documentType_translate: "",
  fileType_translate: "",
});


const dialogTitle = ref(computed(() => t('wordPreview.previewDocument')));
const dialogVisible = ref(false);
const router = useRouter();
const userInfo = ref({});
const fullscreen = ref(false);

const getFileType = (url?: string) => {
  if (!url) return "";
  return url.split(".").pop()?.toLowerCase();
};

// 排版切换
const toggleWrapStorage = JSON.parse(localStorage.getItem("toggleWrapStorage") || "false");
const toggleWrap = ref(toggleWrapStorage);

// 文档高度 宽度
const docWidth = ref("100%");
const docHeight = ref('100%');
const calcDocHeight = (isFull: boolean = false) => {
  const coeff = toggleWrap.value ? 2 : 1;
  docHeight.value = (innerHeight - (isFull ? 150 : 320)) / coeff + "px";
};

// 排版切换
const toToggleWrap = () => {
  toggleWrap.value = !toggleWrap.value;
  localStorage.setItem("toggleWrapStorage", String(toggleWrap.value));
  calcDocHeight();
};
const isFullscreen = ref(false);
const toggleFull = (data: boolean) => {
  isFullscreen.value = data;
  calcDocHeight(data);
}

const open = (data: any) => {
  state.id = data.transId || data.id;
  state.fileName = data.fileName;
  state.source_origin = data.fileUrlOrigin || data.origin;
  state.source_translate = data.fileUrlTranslate || data.translate;
  // 判断文件类型
  state.documentType_origin = checkFileType(state.source_origin);
  state.fileType_origin = getFileType(state.source_origin);
  state.documentType_translate = checkFileType(state.source_translate);
  state.fileType_translate = getFileType(state.source_translate);

  dialogVisible.value = true;
  nextTick(() => {
    state.loading = false;
    calcDocHeight();
  })
}

defineExpose({ open });

// 高亮对照开关
const isComparison = ref(false);

const getUserInfo = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      const user = await getUserProfile();
      resolve(user);
    } catch (error) {
      console.error(t('wordPreview.getUserInfoFailed'), error);
      reject(error);
    }
  });
};

const wordPreviewSingleRight = ref();


// 下载文件
function downloadFile(url: string, type?: string) {
  const ext = url.split(".").pop()?.toLowerCase();
  const fileName = state.fileName.split(".").shift();
  const downLoadFileName = type === "origin" ? `${fileName}.${ext}` : `${fileName}${t('wordPreview.translationSuffix')}.${ext}`;
  fetch(encodeURI(url)).then((res) => {
    res.blob().then((myBlob) => {
      const href = URL.createObjectURL(myBlob);
      const a = document.createElement("a");
      a.href = href;
      a.download = downLoadFileName; // 下载文件重命名，并指定文件扩展名为 ".docx"
      document.body.appendChild(a); // 将<a>元素添加到文档中，以便进行点击下载
      a.click();
      document.body.removeChild(a); // 下载完成后移除<a>元素
      URL.revokeObjectURL(href);
    });
  });
}

const downloadWord = async (type: string) => {
  if (type === "origin") {
    downloadFile(state.source_origin, "origin");
  } else {
    downloadFile(state.source_translate);
  }
};


const fileNameStr = computed(() => {
  const ext = state.fileName.split(".").pop()?.toLowerCase();
  return state.fileName.replace(`.${ext}`, `${t('wordPreview.translationSuffix')}.${ext}`);
});

const getUuid = function () {
  return "xxxxxxxxxxxxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

const VITE_ONLY_OFFICE_BASE_URL = import.meta.env.VITE_ONLY_OFFICE_BASE_URL;
const VITE_ONLY_OFFICE_CALLBACK_URL = import.meta.env.VITE_ONLY_OFFICE_CALLBACK_URL;

onMounted(async () => {
  await getUserInfo().then((res) => {
    userInfo.value = {
      id: res.id + "",
      name: res.nickname || "",
      email: res.email || "",
      avatar: res.avatar || "",
    };
    state.loading = false;
  });

  // resize 修改docHeight
  window.addEventListener("resize", calcDocHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", calcDocHeight);
});

let sourceConnector = null;
let targetConnector = null;
let sourceClickParagraphId = null;
let targetClickParagraphId = null;
const sourceFileKey = `${getUuid()}_origin`;
const targetFileKey = `${getUuid()}_translate`;

const onDocumentReadySource = () => {
  sourceConnector = DocEditor.instances.source.createConnector();
  sourceConnector.attachEvent("onClickParagraph", function (data) {
    if (data.paragraphId) {
      sourceClickParagraphId = data.paragraphId;
      targetClickParagraphId = data.paragraphId;
      sourceFindParagraphIndex(data.paragraphId);
    }
  });

  // 记录滚动位置 - 防抖
  sourceConnector.attachEvent(
    "onScrollAction",
    debounce((data) => {
      localStorage.setItem(`scrollPositionLeft_preview_${state.id}`, JSON.stringify(data));
    })
  );

  const scrollPositionLeft = localStorage.getItem(`scrollPositionLeft_preview_${state.id}`);
  if (scrollPositionLeft) {
    const { type = "vertical", currentPos = 0, limit = 0 } = JSON.parse(scrollPositionLeft);
    Asc.scope = {
      type,
      currentPos,
      limit,
    };
    sourceConnector.callCommand(
      function () {
        Api.ScrollAction("toPos", Asc.scope.type, Asc.scope.currentPos, Asc.scope.limit);
      },
      function () { }
    );
  }
};
const onDocumentReadyTarget = () => {
  targetConnector = DocEditor.instances.target.createConnector();

  targetConnector.attachEvent("onClickParagraph", function (data) {
    if (data.paragraphId) {
      targetClickParagraphId = data.paragraphId;
      targetFindParagraphIndex(data.paragraphId);
    }
  });

  // 记录滚动位置 - 防抖
  targetConnector.attachEvent(
    "onScrollAction",
    debounce((data) => {
      localStorage.setItem(`scrollPositionRight_preview_${state.id}`, JSON.stringify(data));
    })
  );
  const scrollPositionRight = localStorage.getItem(`scrollPositionRight_preview_${state.id}`);

  if (scrollPositionRight) {
    // 恢复滚动位置
    const { type = "vertical", currentPos = 0, limit = 0 } = JSON.parse(scrollPositionRight);
    Asc.scope = {
      type,
      currentPos,
      limit,
    };
    targetConnector.callCommand(
      function () {
        Api.ScrollAction("toPos", Asc.scope.type, Asc.scope.currentPos, Asc.scope.limit);
      },
      function () { }
    );
  }
};

const { t } = useI18n();
const curLang = computed(() => i18n.global.locale);

const customization = ref({
  // 自动保存可以关闭，常规ctrl+s更好用
  autosave: false,
  forcesave: false,
  help: false,
  about: false,

  hideNotes: true,
  hideRightMenu: true,
  hideRulers: true,

  macros: false,
  comments: false,
  mentionShare: false,
  compactHeader: true,
  compactToolbar: true,
  compatibleFeatures: true,
  toolbarHideFileName: true,

  feedback: false,
  plugins: true,

  // layout: {
  //   header: {
  //     editMode: false,
  //     save: false,
  //     users: false,
  //   },
  //   leftMenu: {
  //     mode: false,
  //     navigation: true,
  //     spellcheck: true,
  //   },
  //   rightMenu: {
  //     mode: false,
  //   },
  //   statusBar: false,
  //   toolbar: {
  //     collaboration: {
  //       mailmerge: false,
  //     },
  //     draw: false,
  //     file: {
  //       close: true,
  //       info: true,
  //       save: true,
  //       settings: true,
  //     },
  //     home: {},
  //     layout: true,
  //     plugins: false,
  //     data: false,
  //     protect: false,
  //     references: true,
  //     save: true,
  //     view: {
  //       navigation: true,
  //     },
  //   },
  // },

  logo: {
    // logo配置
    image: imgLogo,
    imageDark: imgLogo,
    // url: "http://36.156.124.58:8051",
    visible: false,
  },
  // features: {
  //   roles: false,
  //   spellcheck: {
  //     mode: false,
  //     change: false,
  //   },
  //   tabBackground: {
  //     mode: "toolbar",
  //     change: true,
  //   },
  //   tabStyle: {
  //     mode: "line",
  //     change: true,
  //   },
  // },
  review: {
    trackChanges: false,
    // hideReviewDisplay: true,
    // showReviewChanges: false,
    // reviewDisplay: "edit",
    // hoverMode: false,
  },
});

const editorConfigOirgin = ref({
  mode: "view", // edit、view
  // callbackUrl: "https://api.docs.onlyoffice.com/dummyCallback",
  callbackUrl: `${VITE_ONLY_OFFICE_CALLBACK_URL}?id=${state.id}&type=origin&url=${state.source_origin}`,
  lang: curLang.value,
});

const editorConfigTranslate = ref({
  mode: "view", // edit、view
  // callbackUrl: "https://api.docs.onlyoffice.com/dummyCallback",
  callbackUrl: `${VITE_ONLY_OFFICE_CALLBACK_URL}?id=${state.id}&type=translate&url=${state.source_translate}`,
  lang: curLang.value,
});

let sourceConfig = computed(() => ({
  type: "desktop",
  // "type": "mobile",
  documentType: state.documentType_origin,
  // historyList: {
  //   history: [],
  //   currentVersion: "1",
  // },
  document: {
    title: state.fileName,
    url: state.source_origin,
    permissions: {
      print: false,
      download: true,
      // edit: true,
    },
    fileType: state.fileType_origin,
    key: sourceFileKey.value,
  },
  editorConfig: {
    user: userInfo.value,
    customization: customization.value,
    ...editorConfigOirgin.value,
  },
}));

var targetConfig = computed(() => ({
  type: "desktop",
  // "type": "mobile",
  documentType: state.documentType_translate,
  // historyList: {
  //   history: [],
  //   currentVersion: "1",
  // },
  document: {
    title: fileNameStr.value,
    url: state.source_translate,
    permissions: {
      print: false,
      download: true,
      // edit: true,
    },
    fileType: state.fileType_translate,
    key: targetFileKey.value,
  },
  editorConfig: {
    user: userInfo.value,
    customization: customization.value,
    ...editorConfigTranslate.value,
  },
}));

// 切换对照插件
const toggleComparison = () => {
  isComparison.value = !isComparison.value;
  if (!isComparison.value) {
    clearnLastHighLight();
  }
};
let sourceFindParagraphIndex = function (paragraphId) {
  if (!isComparison.value) return;
  Asc.scope = {
    paragraphId: paragraphId,
  };
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraphIndex = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        var paragraph = allParagraphs[i];
        if (Asc.scope.paragraphId == paragraph.GetId()) {
          paragraphIndex = i;
          break;
        }
      }
      return paragraphIndex;
    },
    function (paragraphIndex) {
      if (paragraphIndex != null) {
        // 原文点击的时候，同步更新一下targetClickParagraphId
        Asc.scope = {
          paragraphIndex: paragraphIndex,
        };
        targetConnector.callCommand(
          function () {
            var oDocument = Api.GetDocument();
            var allParagraphs = oDocument.GetAllParagraphs();
            var paragraphId = null;
            for (var i = 0; i < allParagraphs.length; i++) {
              var paragraph = allParagraphs[i];
              if (Asc.scope.paragraphIndex == i) {
                paragraphId = paragraph.GetId();
                break;
              }
            }
            return paragraphId;
          },
          function (paragraphId) {
            if (paragraphId != null) {
              targetClickParagraphId = paragraphId;
              highLightParagraph(sourceConnector, paragraphIndex);
              highLightParagraph(targetConnector, paragraphIndex);
            }
          }
        );
      }
    }
  );
};

let targetFindParagraphIndex = function (paragraphId) {
  if (!isComparison.value) return;
  Asc.scope = {
    paragraphId: paragraphId,
  };
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      var allParagraphs = oDocument.GetAllParagraphs();
      var paragraphIndex = null;
      for (var i = 0; i < allParagraphs.length; i++) {
        var paragraph = allParagraphs[i];
        if (Asc.scope.paragraphId == paragraph.GetId()) {
          paragraphIndex = i;
          break;
        }
      }
      return paragraphIndex;
    },
    function (paragraphIndex) {
      if (paragraphIndex != null) {
        // 译文点击的时候，同步更新一下sourceClickParagraphId
        Asc.scope = {
          paragraphIndex: paragraphIndex,
        };
        sourceConnector.callCommand(
          function () {
            var oDocument = Api.GetDocument();
            var allParagraphs = oDocument.GetAllParagraphs();
            var paragraphId = null;
            for (var i = 0; i < allParagraphs.length; i++) {
              var paragraph = allParagraphs[i];
              if (Asc.scope.paragraphIndex == i) {
                paragraphId = paragraph.GetId();
                break;
              }
            }
            return paragraphId;
          },
          function (paragraphId) {
            if (paragraphId != null) {
              sourceClickParagraphId = paragraphId;
              highLightParagraph(sourceConnector, paragraphIndex);
              highLightParagraph(targetConnector, paragraphIndex);
            }
          }
        );
      }
    }
  );
};

/**
 * 设置段落高亮
 * 并且跳转到该位置
 **/
let highLightParagraph = function (connectorInstance, paragraphIndex) {
  Asc.scope = {
    paragraphIndex: paragraphIndex,
  };
  connectorInstance.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      if (oDocument.HighLightParagraphByIndex) {
        oDocument.HighLightParagraphByIndex("index", [Asc.scope.paragraphIndex], true);
      }
    },
    function (paragraphIndex) { }
  );
};

const clearnLastHighLight = function () {
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      if (oDocument.ResetHighLightParagraph) {
        oDocument.ResetHighLightParagraph();
      }
    },
    function (paragraphIndex) { }
  );
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      if (oDocument.ResetHighLightParagraph) {
        oDocument.ResetHighLightParagraph();
      }
    },
    function (paragraphIndex) { }
  );
};

const scrollToParagraph = function (id, date) {
  Asc.scope = {
    Id: id,
    Date: date,
  };
  sourceConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      oDocument.SelectRevisionChange2(Asc.scope.Id, Asc.scope.Date);
    },
    function () { }
  );
};
const scrollToParagraph_target = function (id, date) {
  Asc.scope = {
    Id: id,
    Date: date,
  };
  targetConnector.callCommand(
    function () {
      var oDocument = Api.GetDocument();
      oDocument.SelectRevisionChange2(Asc.scope.Id, Asc.scope.Date);
    },
    function () { }
  );
};
</script>
<style lang="scss" scoped>
@use "../TextPreview/index.scss" as *;

.word-preview {
  height: 100%;
  position: relative;
  padding-bottom: 20px;
}

.word-flex-box {
  height: 100%;
  display: flex;
  gap: 10px;
  justify-content: space-between;

  &.toggleWrap {
    flex-direction: column;

    .word-flex-wrap {}
  }
}

.word-flex-wrap {
  height: 100%;
  /* 已移至模板内联样式绑定 */
  flex: 1;
  position: relative;
  background-color: #e9e9e9;
  box-sizing: border-box;
  overflow: auto;
  border-radius: 8px;
}

.word-wrap {
  // overflow-y: auto;
}
</style>
