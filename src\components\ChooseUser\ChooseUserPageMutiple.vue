<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1000" center>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item :label="t('translate.file.userAccount')" prop="username">
        <el-input v-model="queryParams.username" :placeholder="t('translate.file.userAccountPlaceholder')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('translate.file.userName')" prop="nickname">
        <el-input v-model="queryParams.nickname" :placeholder="t('translate.file.userNamePlaceholder')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="queryParams.mobile" placeholder="请输入手机号码" clearable @keyup.enter="handleQuery" />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          {{ t('translate.file.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          {{ t('translate.file.reset') }}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 已选择用户显示区域 -->
    <div v-if="selectedUsers.length > 0" class="selected-users-area">
      <div class="selected-title">{{ t('translate.file.selectedUsers') }} ({{ selectedUsers.length }})</div>
      <div class="selected-users">
        <el-tag v-for="user in selectedUsers" :key="user.id" closable @close="removeSelectedUser(user.id)" class="selected-user-tag"> {{ user.nickname }}({{ user.username }}) </el-tag>
      </div>
    </div>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" row-key="id">
      <el-table-column type="selection" width="55">
        <template #default="scope">
          <el-checkbox :model-value="isUserSelected(scope.row.id)" @change="(val) => handleSelectionChange(val, scope.row)" />
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.file.userAccount')" align="center" prop="username" :show-overflow-tooltip="true" />
      <el-table-column :label="t('translate.file.userName')" align="center" prop="nickname" :show-overflow-tooltip="true" />
      <!-- <el-table-column label="手机号码" align="center" prop="mobile" :show-overflow-tooltip="true" /> -->
      <el-table-column :label="t('translate.file.status')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="table-ppgination">
      <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="loading" type="primary" @click="submitForm">{{ t('translate.file.confirmSelection') }}</el-button>
        <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" name="SystemChooseUser" setup>
import * as UserApi from "@/api/system/user";
import { DICT_TYPE } from "@/utils/dict";
import { useI18n } from "vue-i18n";

const emit = defineEmits(["set-user-list"]); // 定义emit，改为set-user-list

const { t } = useI18n();
const message = useMessage(); // 消息弹窗

const dialogVisible = ref(false); // 弹窗的是否展示
const dialogTitle = ref(computed(() => t('translate.file.chooseShareUser'))); // 弹窗的标题
const loading = ref(false); // 列表的加载中
const total = ref(0); // 列表的总页数
const list = ref([]); // 列表的数据
const selectedUsers = ref<any[]>([]); // 已选择的用户列表，支持跨页多选

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  username: "",
  nickname: "",
  mobile: "",
});
const queryFormRef = ref(); // 搜索的表单

/** 查询用户列表 */
const getList = async () => {
  loading.value = true;
  try {
    queryParams.nickname = queryParams.nickname ? decodeURIComponent(queryParams.nickname) : "";
    const data = await UserApi.getUserPage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields();
  handleQuery();
};

/** 判断用户是否已选择 */
const isUserSelected = (userId: number) => {
  return selectedUsers.value.some((user) => user.id === userId);
};

/** 选择用户 */
const handleSelectionChange = (val: boolean, row: any) => {
  if (val) {
    // 添加用户到选择列表
    if (!isUserSelected(row.id)) {
      selectedUsers.value.push(row);
    }
  } else {
    // 从选择列表中移除用户
    removeSelectedUser(row.id);
  }
};

/** 移除已选择的用户 */
const removeSelectedUser = (userId: number) => {
  const index = selectedUsers.value.findIndex((user) => user.id === userId);
  if (index > -1) {
    selectedUsers.value.splice(index, 1);
  }
};

/** 提交按钮 */
const submitForm = async () => {
  try {
    loading.value = true;
    // 提交被分享人
    if (selectedUsers.value.length > 0) {
      // 返回用户ID数组和用户名称字符串
      const userIds = selectedUsers.value.map((user) => user.id);
      const userNames = selectedUsers.value.map((user) => user.nickname).join("、");

      emit("set-user-list", userIds, userNames);
      dialogVisible.value = false;

      // 清空选择
      selectedUsers.value = [];
    } else {
      message.error(t('translate.file.pleaseSelectShareUser'));
      return;
    }
  } finally {
    loading.value = false;
  }
};

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true;
  // 清空之前的选择
  selectedUsers.value = [];
  await getList();
};

defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
.top-title {
  margin-bottom: 25px;
  color: #e4393c;
  font-size: 16px;
  text-align: center;
}

.selected-users-area {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .selected-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .selected-users {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .selected-user-tag {
      margin: 0;
    }
  }
}

.table-ppgination {
  position: reactive;

  &::after {
    content: "";
    display: block;
    clear: both;
  }
}
</style>
