# 开发环境：本地只启动前端项目，依赖开发环境（后端、APP）
NODE_ENV=development

VITE_DEV=true

# 请求路径
# VITE_BASE_URL='http://localhost:8901'
VITE_BASE_URL='http://***************:18051'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=server
# 上传路径
VITE_UPLOAD_URL='http://localhost:8901/admin-api/infra/file/upload'

# 上传路径
VITE_UPLOAD_DOC_URL='http://localhost:8901/admin-api/system/doc/upload'

# 上传路径
VITE_UPLOAD_TRS_URL='http://localhost:8901/admin-api/system/translate/upload'

# onlyoffice服务地址
VITE_ONLY_OFFICE_BASE_URL='http://*************:9898'
# VITE_ONLY_OFFICE_BASE_URL='http://*************:8051:9898'

#  onlyoffice回调地址
# VITE_ONLY_OFFICE_CALLBACK_URL='https://api.docs.onlyoffice.com/dummyCallback'
# VITE_ONLY_OFFICE_CALLBACK_URL='https://b058-120-195-55-226.ngrok-free.app/admin-api/system/translate/callback'
VITE_ONLY_OFFICE_CALLBACK_URL='http://*************:8051/admin-api/system/translate/callback'

# 接口地址前缀
VITE_API_URL=/admin-api

# 代理-接口地址
# VITE_APP_PROXY_BASE_URL='http://*************:18051'
# VITE_APP_PROXY_BASE_URL='http://*************:8051'
VITE_APP_PROXY_BASE_URL='http://*************:8051'

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=true

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://mall.yudao.iocoder.cn'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=true
