import type { RouteMeta } from 'vue-router'
import { Icon } from '@/components/Icon'
import { useI18n } from '@/hooks/web/useI18n'
import { getMenuTitle } from '@/utils/menuI18n'

export const useRenderMenuTitle = () => {
  const renderMenuTitle = (meta: RouteMeta) => {
    const { t } = useI18n()
    const { title = 'Please set title', icon } = meta
    const menuTitle = getMenuTitle(title, t)

    return icon ? (
      <>
        <Icon icon={meta.icon}></Icon>
        <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap" title={getMenuTitle(title as string, t)}>
          {getMenuTitle(title as string, t)}
        </span>
      </>
    ) : (
      <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap" title={getMenuTitle(title as string, t)}>
        {getMenuTitle(title as string, t)}
      </span>
    )
  }

  return {
    renderMenuTitle
  }
}
