<template>
  <div class="home-content">
    <div class="bottom" :class="curLang && 'en'">
      <span class="welcome-zh">{{ t('home.mainTitle') }}</span>
      <span class="welcome-en">{{ t('home.subTitle') }}</span>
      <div class="home-href">
        <div class="home-button" @click="handleClick('/translate/text')">
          <img src="@/assets/ai/trans.png" />
          <span class="text">{{ t('home.textTranslation') }}</span>
        </div>
        <div class="home-button" @click="handleClick('/translate/file')">
          <img src="@/assets/ai/doc.png" />
          <span class="text">{{ t('home.documentTranslation') }}</span>
        </div>
      </div>
    </div>
    <PopVersion />
  </div>
</template>
<script lang="ts" name="Home" setup>
import { useRouter } from "vue-router";
import PopVersion from '@/components/PopVersion/index.vue';
import { useI18n } from 'vue-i18n';
import { i18n } from '@/plugins/vueI18n';

const { t } = useI18n();
const router = useRouter();
const curLang = computed(() => i18n.global.locale);

const handleClick = (path) => {
  router.push(path);
};
</script>

<style lang="scss" scoped>
.home-content {
  position: absolute;
  top: 0;
  left: 0;
  overflow: auto;
  height: calc(100% - 25px);
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: url("../../assets/imgs/home.jpg") center / cover no-repeat;

  img {
    width: 40%;
  }

  .bottom {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    margin-top: -100px;

    &.en {
      .welcome-zh {
        font-size: 50px;
        letter-spacing: 0;
      }
      .welcome-en {
        font-size: 35px;
        letter-spacing: 0;
      }
    }

    span {
      font-family: "阿里巴巴普惠体";
    }

    .welcome-zh {
      font-size: 56px;
      color: #10222c;
      font-weight: 900;
      letter-spacing: 5px;
    }

    .welcome-en {
      font-size: 56px;
      margin-top: 30px;
      color: #0d54ff;
      font-weight: 900;
      letter-spacing: 5px;
    }

    .home-href {
      display: flex;
      justify-content: center;
      margin-top: 60px;

      .home-button {
        margin: 0 20px;
        background: #ffffff;
        border-radius: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 8px 20px;
        cursor: pointer;

        img {
          width: 35px;
          height: 35px;
          margin-right: 10px;
          margin-left: -8px;
        }

        .text {
          font-size: 20px;
          color: #1b1c1f;
        }
      }
    }
  }
}
</style>
<script setup></script>
