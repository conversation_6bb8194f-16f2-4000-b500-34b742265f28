<template>
  <div class="preview-box">
    <p class="title">{{ fileName.split(".")[0] }}</p>
    <el-space v-if="loading" direction="horizontal" wrap fill :fill-ratio="40" style="width: 100%">
      <div class="space-item">
        <el-skeleton animated :rows="20" :count="1" />
      </div>
      <div class="space-item space-item2">
        <el-skeleton animated :rows="20" :count="1" />
      </div>
    </el-space>
    <div class="showPreview" v-else>
      <div class="show-word">
        <WordPreview :updateTime="updateTime" :wordUrlList="wordUrlList" :fileName="fileName" :id="curId" :scene="scene" @to-show-table="toShowTable" />
         <!-- <WordContrast /> -->
      </div>
    </div>
  </div>
  <HistoryTable ref="historyTableRef" @to-show-detail-table="toShowDetailTable" />
  <EditForm ref="editForm" :whichPannel="whichPannel" />
</template>

<script setup lang="ts">
import { DocTranslateApi } from "@/api/system/translate";
import WordPreview from "@/components/WordPreview/index.vue";
import WordContrast from "@/components/WordContrast/index-old.vue";
import HistoryTable from "./historyTable.vue";
import EditForm from "@/components/TextPreview/EditForm.vue";
import { useI18n } from 'vue-i18n';

// 当前修改的左边还是右边的 判断
const whichPannel = ref("right");

defineOptions({ name: "TranslatePreview" });

const { t } = useI18n();
const router = useRouter();
const message = useMessage(); // 消息弹窗

const loading = ref(true); // 列表的加载中

const currentDate = new Date().toDateString();
const curId: any = router.currentRoute.value.query.id || "";

const pdfUrlList = ref(["", ""]);
const wordUrlList = ref(["", ""]);
const excelUrlList = ref(["", ""]);
const fileName = ref("");
const updateTime = ref(0);
const scene = ref(1);

const historyTableRef = ref();
const toShowTable = () => {
  historyTableRef.value.open(curId);
};

const editForm = ref();
const toShowDetailTable = (id) => {
  editForm.value.showDetail(id);
};
/** 查询详情 */
const fetchData = async () => {
  loading.value = true;
  try {
    const data = await DocTranslateApi.getDocTranslate(curId);
    // 根据文件名获取文件类型
    updateTime.value = data.updateTime;
    fileName.value = data.fileName;
    scene.value = data.scene;
    const fileType = data.fileName?.split(".")?.pop();

    // // 开发环境 TODO: 本地测试使用
    // if (import.meta.env.DEV) {
    //   if (data.fileUrlOrigin) {
    //     data.fileUrlOrigin = data.fileUrlOrigin.replace(import.meta.env.VITE_APP_PROXY_FILES_URL, "/file-api");
    //   }
    //   if (data.fileUrlTranslate) {
    //     data.fileUrlTranslate = data.fileUrlTranslate.replace(import.meta.env.VITE_APP_PROXY_FILES_URL, "/file-api");
    //   }
    // }
    const list = [data.fileUrlOrigin || "", data.fileUrlTranslate || ""];

    wordUrlList.value = list;
    // if (fileType === "pdf") {
    //   pdfUrlList.value = list
    // } else if (fileType === "docx") {
    //   wordUrlList.value = list
    // } else if (fileType === "xlsx") {
    //   excelUrlList.value = list
    // } else {
    //   message.error("暂不支持该文件类型");
    // }
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.preview-box {
  width: 100%;
  color: #1d2129;
  background: #fff;
  // border: 1px solid #f2f3f5;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgb(35 46 67 / 6%);
  padding: 0 30px 30px 30px;
  position: relative;

  .title {
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
  }

  .space-item {
    width: 50%;
    padding: 0 20px 0 0;
    border-right: 1px solid #f2f3f5;

    &.space-item2 {
      border: none;
      padding: 0 0 0 10px;
    }
  }
}
</style>
