<template>
  <Dialog v-model="dialogVisible" title="审核" width="600">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="质疑内容" prop="result">
        <el-input
          v-model="formData.result"
          :rows="3"
          type="textarea"
          placeholder="请输入质疑内容"
        />
      </el-form-item>
      <el-form-item label="质疑分类" prop="status">
        <el-select v-model="formData.status" placeholder="请选择结果" clearable>
          <el-option
            v-for="dict in statusList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="责任人" prop="user">
        <el-input v-model="formData.user" placeholder="请输入责任人" />
      </el-form-item>
      <el-form-item label="目标解决日期" prop="date">
        <el-date-picker
          type="date"
          v-model="formData.date"
          placeholder="请选择目标解决日期"
          value-format="yyyy-MM-dd"
          style="width: 100%"
          :picker-options="{ firstDayOfWeek: 1 }"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" name="AuditForm" setup>
import { DocFileApi } from "@/api/system/doc";

const message = useMessage();
const dialogVisible = ref(false); // 弹窗的是否展示
const formLoading = ref(false); // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const statusList = [
  { label: "演示审批流", value: 1 },
  { label: "其它", value: 2 },
];
const formData = ref({
  id: undefined,
  result: undefined,
  status: undefined,
  date: "",
  user: undefined,
  remark: "",
});
const formRules = reactive({
  // status: [{required: true, message: '审核结果不能为空', trigger: 'blur'}],
  // reason: [{required: true, message: '不通过理由不能为空', trigger: 'blur'}]
});
const formRef = ref(); // 表单 Ref
/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true;
  resetForm();
  // 修改时，设置数据
  if (id) {
    formData.value.id = id;
    formLoading.value = true;
    try {
      formData.value = await DocFileApi.getDocFile(id);
    } finally {
      formLoading.value = false;
    }
  }
};
defineExpose({ open }); // 提供 open 方法，用于打开弹窗
interface Emits {
  (event: "success", name: any): void;
}

/** 提交表单 */
const emit = defineEmits<Emits>(); // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return;
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  formLoading.value = true;
  try {
    const data = formData.value as unknown as TaskApi.TaskVO;
    await DocFileApi.updateDocFile(data);
    message.success("审核成功");
    dialogVisible.value = false;
    emit("success", formData.value.status);
  } finally {
    formLoading.value = false;
  }
};
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: 0,
    status: undefined,
    remark: "",
    deptId: undefined,
  };
  formRef.value?.resetFields();
};
</script>
