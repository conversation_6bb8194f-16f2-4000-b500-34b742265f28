export default {
  app: {
    title: 'Hengrui Large Model Platform'
  },
  common: {
    inputText: 'Please input',
    selectText: 'Please select',
    startTimeText: 'Start time',
    endTimeText: 'End time',
    login: 'Login',
    required: 'This is required',
    loginOut: 'Login out',
    document: 'Document',
    profile: 'User Center',
    reminder: 'Reminder',
    loginOutMessage: 'Exit the system?',
    back: 'Back',
    ok: 'OK',
    save: 'Save',
    close: 'Close',
    reload: 'Reload current',
    success: 'Success',
    closeTab: 'Close current',
    closeTheLeftTab: 'Close left',
    closeTheRightTab: 'Close right',
    closeOther: 'Close other',
    closeAll: 'Close all',
    prevLabel: 'Prev',
    nextLabel: 'Next',
    skipLabel: 'Jump',
    doneLabel: 'End',
    menu: 'Menu',
    menuDes: 'Menu bar rendered in routed structure',
    collapse: 'Collapse',
    collapseDes: 'Expand and zoom the menu bar',
    tagsView: 'Tags view',
    tagsViewDes: 'Used to record routing history',
    tool: 'Tool',
    toolDes: 'Used to set up custom systems',
    query: 'Query',
    shrink: 'Put away',
    expand: 'Expand',
    confirmTitle: 'System Hint',
    exportMessage: 'Whether to confirm export data item?',
    importMessage: 'Whether to confirm import data item?',
    createSuccess: 'Create Success',
    updateSuccess: 'Update Success',
    delMessage: 'Delete the selected data?',
    delDataMessage: 'Delete the data?',
    delNoData: 'Please select the data to delete',
    delSuccess: 'Deleted successfully',
    index: 'Index',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    copy: 'Copy',
    copySuccess: 'Copy Success',
    copyError: 'Copy Error',
    tip: 'Tip',
    action: 'Action',
    delete: 'Delete',
    deleteSuccess: 'Delete Success',
    deleteError: 'Delete Error',
    cancel: 'Cancel',
    confirm: 'Confirm',
    search: 'Search',
    add: 'Add',
    addSuccess: 'Add Success',
    addError: 'Add Error',
    edit: 'Edit',
    editSuccess: 'Edit Success',
    editError: 'Edit Error',
    export: 'Export',
    exportSuccess: 'Export Success',
    exportError: 'Export Error',
    import: 'Import',
    importSuccess: 'Import Success',
    importError: 'Import Error',
    generate: 'Generate',
    generateSuccess: 'Generate Success',
    generateError: 'Generate Error',
    logout: 'Logout',
    logoutSuccess: 'Logout Success',
    logoutError: 'Logout Error',
    test: 'Test',
    testSuccess: 'Test Success',
    testError: 'Test Error',
    refresh: 'Refresh',
    refreshSuccess: 'Refresh Success',
    refreshError: 'Refresh Error',
    reset: 'Reset',
    resetSuccess: 'Reset Success',
    resetError: 'Reset Error',
    creator: 'Creator',
    actions: 'Actions',
    startDate: 'Start Date',
    endDate: 'End Date',
    noEmpty: 'No Empty',
    submit: 'Submit',
    submitSuccess: 'Submit Success',
    idError: 'Can Not Find ID',
  },
  lock: {
    lockScreen: 'Lock screen',
    lock: 'Lock',
    lockPassword: 'Lock screen password',
    unlock: 'Click to unlock',
    backToLogin: 'Back to login',
    entrySystem: 'Entry the system',
    placeholder: 'Please enter the lock screen password',
    message: 'Lock screen password error'
  },
  error: {
    noPermission: `Sorry, you don't have permission to access this page.`,
    pageError: 'Sorry, the page you visited does not exist.',
    networkError: 'Sorry, the server reported an error.',
    returnToHome: 'Return to home'
  },
  permission: {
    hasPermission: `Please set the operation permission label value`,
    hasRole: `Please set the role permission tag value`
  },
  setting: {
    projectSetting: 'Project setting',
    theme: 'Theme',
    layout: 'Layout',
    systemTheme: 'System theme',
    menuTheme: 'Menu theme',
    interfaceDisplay: 'Interface display',
    breadcrumb: 'Breadcrumb',
    breadcrumbIcon: 'Breadcrumb icon',
    collapseMenu: 'Collapse menu',
    hamburgerIcon: 'Hamburger icon',
    screenfullIcon: 'Screenfull icon',
    sizeIcon: 'Size icon',
    localeIcon: 'Locale icon',
    messageIcon: 'Message icon',
    tagsView: 'Tags view',
    logo: 'Logo',
    greyMode: 'Grey mode',
    fixedHeader: 'Fixed header',
    headerTheme: 'Header theme',
    cutMenu: 'Cut Menu',
    copy: 'Copy',
    clearAndReset: 'Clear cache and reset',
    copySuccess: 'Copy success',
    copyFailed: 'Copy failed',
    footer: 'Footer',
    uniqueOpened: 'Unique opened',
    tagsViewIcon: 'Tags view icon',
    reExperienced: 'Please exit the login experience again',
    fixedMenu: 'Fixed menu'
  },
  size: {
    default: 'Default',
    large: 'Large',
    small: 'Small'
  },
  login: {
    welcome: 'Welcome to the system',
    message: 'Backstage management system',
    tenantname: 'TenantName',
    username: 'Username',
    password: 'Password',
    code: 'verification code',
    login: 'Sign in',
    relogin: 'Sign in again',
    otherLogin: 'Sign in with',
    register: 'Register',
    checkPassword: 'Confirm password',
    remember: 'Remember me',
    hasUser: 'Existing account? Go to login',
    forgetPassword: 'Forget password?',
    tenantNamePlaceholder: 'Please Enter Tenant Name',
    usernamePlaceholder: 'Please Enter Username',
    passwordPlaceholder: 'Please Enter Password',
    codePlaceholder: 'Please Enter Verification Code',
    mobileTitle: 'Mobile sign in',
    mobileNumber: 'Mobile Number',
    mobileNumberPlaceholder: 'Plaease Enter Mobile Number',
    backLogin: 'back',
    getSmsCode: 'Get SMS Code',
    btnMobile: 'Mobile sign in',
    btnQRCode: 'QR code sign in',
    qrcode: 'Scan the QR code to log in',
    btnRegister: 'Sign up',
    SmsSendMsg: 'code has been sent'
  },
  captcha: {
    verification: 'Please complete security verification',
    slide: 'Swipe right to complete verification',
    point: 'Please click',
    success: 'Verification succeeded',
    fail: 'verification failed'
  },
  router: {
    login: 'Login',
    home: 'Home',
    analysis: 'Analysis',
    workplace: 'Workplace'
  },
  analysis: {
    newUser: 'New user',
    unreadInformation: 'Unread information',
    transactionAmount: 'Transaction amount',
    totalShopping: 'Total Shopping',
    monthlySales: 'Monthly sales',
    userAccessSource: 'User access source',
    january: 'January',
    february: 'February',
    march: 'March',
    april: 'April',
    may: 'May',
    june: 'June',
    july: 'July',
    august: 'August',
    september: 'September',
    october: 'October',
    november: 'November',
    december: 'December',
    estimate: 'Estimate',
    actual: 'Actual',
    directAccess: 'Airect access',
    mailMarketing: 'Mail marketing',
    allianceAdvertising: 'Alliance advertising',
    videoAdvertising: 'Video advertising',
    searchEngines: 'Search engines',
    weeklyUserActivity: 'Weekly user activity',
    activeQuantity: 'Active quantity',
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday'
  },
  workplace: {
    welcome: 'Hello',
    happyDay: 'Wish you happy every day!',
    toady: `It's sunny today`,
    notice: 'Announcement',
    project: 'Project',
    access: 'Project access',
    toDo: 'To do',
    introduction: 'A serious introduction',
    shortcutOperation: 'Quick entry',
    operation: 'Operation',
    index: 'Index',
    personal: 'Personal',
    team: 'Team',
    quote: 'Quote',
    contribution: 'Contribution',
    hot: 'Hot',
    yield: 'Yield',
    dynamic: 'Dynamic',
    push: 'push',
    follow: 'Follow'
  },
  form: {
    input: 'Input',
    inputNumber: 'InputNumber',
    default: 'Default',
    icon: 'Icon',
    mixed: 'Mixed',
    textarea: 'Textarea',
    slot: 'Slot',
    position: 'Position',
    autocomplete: 'Autocomplete',
    select: 'Select',
    selectGroup: 'Select Group',
    selectV2: 'SelectV2',
    cascader: 'Cascader',
    switch: 'Switch',
    rate: 'Rate',
    colorPicker: 'Color Picker',
    transfer: 'Transfer',
    render: 'Render',
    radio: 'Radio',
    button: 'Button',
    checkbox: 'Checkbox',
    slider: 'Slider',
    datePicker: 'Date Picker',
    shortcuts: 'Shortcuts',
    today: 'Today',
    yesterday: 'Yesterday',
    aWeekAgo: 'A week ago',
    week: 'Week',
    year: 'Year',
    month: 'Month',
    dates: 'Dates',
    daterange: 'Date Range',
    monthrange: 'Month Range',
    dateTimePicker: 'DateTimePicker',
    dateTimerange: 'Datetime Range',
    timePicker: 'Time Picker',
    timeSelect: 'Time Select',
    inputPassword: 'input Password',
    passwordStrength: 'Password Strength',
    operate: 'operate',
    change: 'Change',
    restore: 'Restore',
    disabled: 'Disabled',
    disablement: 'Disablement',
    delete: 'Delete',
    add: 'Add',
    setValue: 'Set value',
    resetValue: 'Reset value',
    set: 'Set',
    subitem: 'Subitem',
    formValidation: 'Form validation',
    verifyReset: 'Verify reset',
    remark: 'Remark'
  },
  watermark: {
    watermark: 'Watermark'
  },
  table: {
    table: 'Table',
    index: 'Index',
    title: 'Title',
    author: 'Author',
    createTime: 'Create time',
    action: 'Action',
    pagination: 'pagination',
    reserveIndex: 'Reserve index',
    restoreIndex: 'Restore index',
    showSelections: 'Show selections',
    hiddenSelections: 'Restore selections',
    showExpandedRows: 'Show expanded rows',
    hiddenExpandedRows: 'Hidden expanded rows',
    header: 'Header'
  },
  action: {
    create: 'Create',
    add: 'Add',
    del: 'Delete',
    delete: 'Delete',
    edit: 'Edit',
    update: 'Update',
    preview: 'Preview',
    more: 'More',
    sync: 'Sync',
    save: 'Save',
    detail: 'Detail',
    export: 'Export',
    import: 'Import',
    generate: 'Generate',
    logout: 'Login Out',
    test: 'Test',
    typeCreate: 'Dict Type Create',
    typeUpdate: 'Dict Type Eidt',
    dataCreate: 'Dict Data Create',
    dataUpdate: 'Dict Data Eidt',
    fileUpload: 'File Upload'
  },
  dialog: {
    dialog: 'Dialog',
    open: 'Open',
    close: 'Close'
  },
  sys: {
    api: {
      operationFailed: 'Operation failed',
      errorTip: 'Error Tip',
      errorMessage: 'The operation failed, the system is abnormal!',
      timeoutMessage: 'Login timed out, please log in again!',
      apiTimeoutMessage: 'The interface request timed out, please refresh the page and try again!',
      apiRequestFailed: 'The interface request failed, please try again later!',
      networkException: 'network anomaly',
      networkExceptionMsg:
        'Please check if your network connection is normal! The network is abnormal',

      errMsg401: 'The user does not have permission (token, user name, password error)!',
      errMsg403: 'The user is authorized, but access is forbidden!',
      errMsg404: 'Network request error, the resource was not found!',
      errMsg405: 'Network request error, request method not allowed!',
      errMsg408: 'Network request timed out!',
      errMsg500: 'Server error, please contact the administrator!',
      errMsg501: 'The network is not implemented!',
      errMsg502: 'Network Error!',
      errMsg503: 'The service is unavailable, the server is temporarily overloaded or maintained!',
      errMsg504: 'Network timeout!',
      errMsg505: 'The http version does not support the request!',
      errMsg901: 'Demo mode, no write operations are possible!'
    },
    app: {
      logoutTip: 'Reminder',
      logoutMessage: 'Confirm to exit the system?',
      menuLoading: 'Menu loading...'
    },
    exception: {
      backLogin: 'Back Login',
      backHome: 'Back Home',
      subTitle403: "Sorry, you don't have access to this page.",
      subTitle404: 'Sorry, the page you visited does not exist.',
      subTitle500: 'Sorry, the server is reporting an error.',
      noDataTitle: 'No data on the current page.',
      networkErrorTitle: 'Network Error',
      networkErrorSubTitle:
        'Sorry, Your network connection has been disconnected, please check your network!'
    },
    lock: {
      unlock: 'Click to unlock',
      alert: 'Lock screen password error',
      backToLogin: 'Back to login',
      entry: 'Enter the system',
      placeholder: 'Please enter the lock screen password or user password'
    },
    login: {
      backSignIn: 'Back sign in',
      mobileSignInFormTitle: 'Mobile sign in',
      qrSignInFormTitle: 'Qr code sign in',
      signInFormTitle: 'Sign in',
      signUpFormTitle: 'Sign up',
      forgetFormTitle: 'Reset password',

      signInTitle: 'Backstage management system',
      signInDesc: 'Enter your personal details and get started!',
      policy: 'I agree to the xxx Privacy Policy',
      scanSign: `scanning the code to complete the login`,

      loginButton: 'Sign in',
      registerButton: 'Sign up',
      rememberMe: 'Remember me',
      forgetPassword: 'Forget Password?',
      otherSignIn: 'Sign in with',

      // notify
      loginSuccessTitle: 'Login successful',
      loginSuccessDesc: 'Welcome back',

      // placeholder
      accountPlaceholder: 'Please input username',
      passwordPlaceholder: 'Please input password',
      smsPlaceholder: 'Please input sms code',
      mobilePlaceholder: 'Please input mobile',
      policyPlaceholder: 'Register after checking',
      diffPwd: 'The two passwords are inconsistent',

      userName: 'Username',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      email: 'Email',
      smsCode: 'SMS code',
      mobile: 'Mobile'
    }
  },
  profile: {
    user: {
      title: 'Personal Information',
      username: 'User Name',
      nickname: 'Nick Name',
      mobile: 'Phone Number',
      email: 'User Mail',
      dept: 'Department',
      posts: 'Position',
      roles: 'Own Role',
      sex: 'Sex',
      man: 'Man',
      woman: 'Woman',
      createTime: 'Created Date'
    },
    info: {
      title: 'Basic Information',
      basicInfo: 'Basic Information',
      resetPwd: 'Reset Password',
      userSocial: 'Social Information'
    },
    rules: {
      nickname: 'Please Enter User Nickname',
      mail: 'Please Input The Email Address',
      truemail: 'Please Input The Correct Email Address',
      phone: 'Please Enter The Phone Number',
      truephone: 'Please Enter The Correct Phone Number'
    },
    password: {
      oldPassword: 'Old PassWord',
      newPassword: 'New Password',
      confirmPassword: 'Confirm Password',
      oldPwdMsg: 'Please Enter Old Password',
      newPwdMsg: 'Please Enter New Password',
      cfPwdMsg: 'Please Enter Confirm Password',
      diffPwd: 'The Passwords Entered Twice No Match'
    }
  },
  cropper: {
    selectImage: 'Select Image',
    uploadSuccess: 'Uploaded success!',
    modalTitle: 'Avatar upload',
    okText: 'Confirm and upload',
    btn_reset: 'Reset',
    btn_rotate_left: 'Counterclockwise rotation',
    btn_rotate_right: 'Clockwise rotation',
    btn_scale_x: 'Flip horizontal',
    btn_scale_y: 'Flip vertical',
    btn_zoom_in: 'Zoom in',
    btn_zoom_out: 'Zoom out',
    preview: 'Preivew'
  },
  translate: {
    file: {
      share: 'Share',
      shareUser: 'shareUser',
      acceptShareUser: 'acceptShareUser',
      expireTime: 'expireTime',
      docPermission: 'docPermission',
      shareBack: 'shareBack',
      preview: 'Preview',
      shareTime: 'shareTime',
      expireTimeTip: 'Expire Time Tip',
      fileName: 'File Name',
      fileNamePlaceholder: 'Please enter file name',
      translateType: 'Type',
      selectType: 'Select Translation Type',
      translateStatus: 'Translation Status',
      selectStatus: 'Select Status',
      createTime: 'Create Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      search: 'Search',
      reset: 'Reset',
      uploadDoc: 'Upload Document',
      fileSize: 'File Size',
      creator: 'Creator',
      translateTime: 'Translation Time',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      download: 'Download',
      delete: 'Delete',
      translating: 'Translating...',
      translateError: 'Translation Error',
      downloadSuccess: 'Download Success!',
      dragUploadText: 'Drag files here, or ',
      clickUpload: 'click to upload',
      supportFileTypes: 'Support Word, Excel, PPT, PDF, TXT, RTF document types, multiple document upload, and single document not exceeding {maxFileSize} MB',
      selectScene: 'Select Scene',
      uploadAndTranslate: 'Upload and Translate',
      cancel: 'Cancel',
      unsupportedFileType: 'Please upload supported file types!',
      fileSizeExceeded: 'File size exceeds limit {maxFileSize} MB!',
      selectTranslateType: 'Please select translation type',
      uploadFileFirst: 'Please upload file first',
      aiTranslating: 'AI is accelerating translation...',
      uploadSuccess: 'Upload Success',
      uploadFailed: 'Upload failed, please try again!',
      maxFileLimit: 'Maximum 10 files can be uploaded!',
      // Share form related
      shareFormTitle: 'Document Share',
      selectShareUserPlaceholder: 'Please select share users',
      clickToSelect: 'Click to Select',
      selectShareUserRequired: 'Please select share users',
      selectDocPermissionRequired: 'Please select document permission',
      selectExpireTimeRequired: 'Please select expire time',
      submitSuccess: 'Submit Success!',
      // User selection component related
      chooseShareUser: 'Choose Share Users',
      userAccount: 'User Account',
      userAccountPlaceholder: 'Please enter user account',
      userName: 'User Name',
      userNamePlaceholder: 'Please enter user name',
      status: 'Status',
      selectedUsers: 'Selected Users',
      confirmSelection: 'Confirm Selection',
      pleaseSelectShareUser: 'Please select share users',
    },
    text: {
      translateScene: 'Translation Scene',
      selectTranslateScene: 'Select Translation Scene',
      enableCache: 'Enable Cache',
      inputTextToTranslate: 'Please enter text to translate',
      translation: 'Translation',
      copy: 'Copy',
      chineseSimplified: 'Chinese (Simplified)',
      english: 'English',
      translating: 'Translating...',
      copySuccess: 'Copy Success',
      translateHistory: 'Translate History',
      deleteTip: 'After deleting, it cannot be restored. Do you want to proceed?',
    },
    record: {
      number: 'Number',
      translateScene: 'Scene',
      original: 'Original',
      translate: 'Translate',
      deleteTip: 'After deleting, it cannot be restored. Do you want to proceed?',
      source: 'Source',
      type: 'Type',
      type1: 'Chinese to English',
      type2: 'English to Chinese',
      exportFile: 'Translate History List',
      translateHistoryRecord: 'Translate History Record',
      creator: 'creator',
      createTime: 'createTime'
    },
    ocr: {
      title: 'OCR Image recognition',
      uploadImage: 'Upload Image:',
      recognitionResult: 'Recognition Result:',
      uploadLimit: 'Limited to 1 image per time, image size should not exceed 50MB, format: JPG, PNG',
      pleaseUpload: 'Please upload an image',
      uploadFormatError: 'Only JPG/PNG files are allowed!',
      uploadSizeError: 'Image size cannot exceed 50MB!',
      recognitionFailed: 'Recognition failed, please try again'
    },
    statistics: {
      timeRange: 'Time Range',
      startDate: 'Start Date',
      endDate: 'End Date',
      search: 'Search',
      reset: 'Reset',
      uploadUsers: 'Upload Users',
      totalTranslations: 'Total Translations',
      completedTranslations: 'Completed Translations',
      pendingSubmissions: 'Pending Submissions',
      underReview: 'Under Review',
      approved: 'Approved',
      rejected: 'Rejected',
      quantity: 'Quantity',
      uploadDocumentCount: 'Upload Document Count'
    },
    history: {
      problemReport: 'Problem Report',
      searchOriginal: 'Search Original',
      enterOriginal: 'Please enter original text',
      searchTranslation: 'Search Translation',
      enterModifiedTranslation: 'Please enter modified translation',
      search: 'Search',
      reset: 'Reset',
      serialNumber: 'Number',
      original: 'Original',
      translation: 'Translation',
      modifiedOriginal: 'Modified Original',
      modifiedTranslation: 'Modified Translation',
      remark: 'Remark',
      creator: 'Creator',
      auditor: 'Auditor',
      auditStatus: 'Audit Status',
      createTime: 'Create Time',
      actions: 'Actions',
      edit: 'Edit',
      delete: 'Delete',
      deleteSuccess: 'Delete Success',
      confirmAudit: 'Confirm audit approval?',
      auditInfo: 'Audit Information',
      approve: 'Approve',
      reject: 'Reject',
      auditApproved: 'Audit Approved',
      auditRejected: 'Audit Rejected'
    },
    report: {
      documentName: 'File Name',
      enterDocumentName: 'Please enter document name',
      auditor: 'Auditor',
      enterAuditorName: 'Please enter auditor name',
      searchOriginal: 'Original',
      enterOriginal: 'Please enter original text',
      searchTranslation: 'Translation',
      enterModifiedTranslation: 'Please enter modified translation',
      auditStatus: 'Audit Status',
      selectAuditStatus: 'Please select audit status',
      createTime: 'Create Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      search: 'Search',
      reset: 'Reset',
      export: 'Export',
      number: 'Number',
      original: 'Original',
      translation: 'Translation',
      remark: 'Remark',
      creator: 'Creator',
      actions: 'Actions',
      audit: 'Audit',
      view: 'View',
      delete: 'Delete',
      deleteSuccess: 'Delete Success',
      confirmAudit: 'Confirm audit approval?',
      auditInfo: 'Audit Information',
      approve: 'Approve',
      reject: 'Reject',
      auditApproved: 'Audit Approved',
      auditRejected: 'Audit Rejected',
      problemReportFile: 'Problem Report.xls'
    },
    version: {
      fileName: 'File Name',
      fileNamePlaceholder: 'Please enter file name',
      aiLearning: 'AI Learning',
      selectAiLearning: 'Please select AI learning',
      fileSource: 'File Source',
      selectFileSource: 'Please select file source',
      createTime: 'Create Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      yes: 'Yes',
      no: 'No',
      documentTranslation: 'Document Translation',
      manualUpload: 'Manual Upload',
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      originalLink: 'Original Link',
      translationLink: 'Translation Link',
      currentVersion: 'Current Version',
      actions: 'Actions',
      preview: 'Preview',
      edit: 'Edit',
      historyVersion: 'History Version',
      delete: 'Delete',
      exportFileName: 'Translation Version Info.xls'
    },
    editor: {
      modifyTranslation: 'Modify Translation',
      historyRecords: 'History Records',
      unsupportedFileType: 'Unsupported file type'
    },
    sentence: {
      sentenceComparison: 'Sentence Comparison',
      historyRecords: 'History Records'
    },
    textPreview: {
      paragraphEdit: 'Paragraph Edit',
      share: 'Share',
      shareUser: 'Share User',
      selectUser: 'Please select user',
      sharePermission: 'Share Permission',
      viewOnly: 'View Only',
      viewAndDownload: 'View and Download',
      editable: 'Editable',
      editAndDownload: 'Edit and Download',
      confirm: 'Confirm',
      historyRecords: 'History Records',
      auditStatus: 'Audit Status',
      audit: 'Audit'
    },
    reportEdit: {
      problemReport: 'Problem Report',
      auditor: 'Auditor:',
      original: 'Original:',
      translation: 'Translation:',
      remark: 'Remark',
      enterContent: 'Please enter content',
      close: 'Close'
    },
    versionForm: {
      fileName: 'File Name',
      fileNamePlaceholder: 'Upload original file to auto-fill file name',
      currentVersion: 'Current Version',
      enterCurrentVersion: 'Please enter current version',
      uploadOriginal: 'Upload Original',
      uploadTranslation: 'Upload Translation',
      confirm: 'Confirm',
      cancel: 'Cancel'
    },
    versionHistory: {
      fileSource: 'File Source',
      selectFileSource: 'Please select file source',
      documentTranslation: 'Document Translation',
      manualUpload: 'Manual Upload',
      createTime: 'Create Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      fileName: 'File Name',
      originalLink: 'Original Link',
      translationLink: 'Translation Link',
      currentVersion: 'Current Version',
      actions: 'Actions',
      preview: 'Preview',
      edit: 'Edit',
      delete: 'Delete',
      exportFileName: 'Translation History Version Info.xls'
    },
    uploadFile: {
      delete: 'Delete',
      uploading: 'Uploading, please do not operate or close the page',
      dragText: 'Drag files here, or',
      clickUpload: 'click to upload',
      supportTypes: 'Support Word, Excel, PPT, PDF, TXT, RTF document types, single document not exceeding {maxFileSize} MB',
      unsupportedFileType: 'Please upload supported file types!',
      fileSizeExceeded: 'File size exceeds limit {maxFileSize} MB!',
      uploadSuccess: 'Upload Success',
      uploadFailed: 'Upload failed, please try again!',
      maxOneFile: 'Maximum 1 file can be uploaded!'
    },
    versionHistoryForm: {
      uploadOriginal: 'Upload Original',
      uploadTranslation: 'Upload Translation',
      confirm: 'Confirm',
      cancel: 'Cancel'
    }
  },
  wordPreview: {
    keywordDisplay: 'Keyword Display',
    grammarIssueDisplay: 'Grammar Issue Display',
    templateFormatIssueDisplay: 'Template Format Issue Display',
    problemReportRecords: 'Problem Report Records',
    userRevisionRecord: 'User: {nickName} Revision Record',
    paragraphEdit: 'Paragraph Edit',
    formatEdit: 'Format Edit',
    leftRightLayout: 'Left-Right Layout',
    topBottomLayout: 'Top-Bottom Layout',
    sentenceComparison: 'Sentence Comparison',
    previousPage: 'Previous Page',
    nextPage: 'Next Page',
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    resetZoom: 'Reset Zoom',
    closeComparison: 'Close Comparison',
    openComparison: 'Open Comparison',
    closeRevisionRecord: 'Close Revision Record',
    openRevisionRecord: 'Open Revision Record',
    downloadOriginal: 'Download Original',
    downloadTranslation: 'Download Translation',
    uploadVersion: 'Upload Version',
    retranslate: 'Retranslate',
    original: 'Original',
    oldTranslation: 'Old Translation',
    newTranslation: 'New Translation',
    copyNewTranslation: 'Copy New Translation',
    confirmReplaceTranslation: 'Confirm Replace Old Translation with New Translation',
    cancel: 'Cancel',
    getUserInfoFailed: 'Failed to get user info:',
    copySuccess: 'Copy Success',
    problemReport: 'Problem Report',
    textAdd: 'Text Added',
    textRemove: 'Text Removed',
    paragraphAdd: 'Paragraph Added',
    paragraphRemove: 'Paragraph Removed',
    textStyleModify: 'Text Style Modified',
    paragraphStyleModify: 'Paragraph Style Modified',
    unknown: 'Unknown',
    noTranslationSelected: 'No translation selected',
    newTranslationEmpty: 'New translation is empty',
    translationSuffix: ' (Translation)',
    previewDocument: 'Preview Document',
    reviwer: 'Reviewer Mode'
  },
  menu: {
    // Document Classification Quality Control
    'TMF分类质控': 'TMF Classification Quality Control',
    '统计分析': 'Statistical Analysis',
    '文档上传': 'Document Upload',
    '任务详情': 'Task Details',
    '我的文档': 'My Documents',
    '驳回文档': 'Rejected Documents',

    // AI Translation
    'AI翻译': 'AI Translation',
    '文本翻译': 'Text Translation',
    '翻译历史': 'Translation History',
    '文档翻译': 'Document Translation',
    '整篇预览': 'Full Preview',
    '逐段对照': 'Paragraph Comparison',
    '逐句对照': 'Sentence Comparison',
    '问题上报': 'Problem Report',
    '问题列表': 'Problem List',
    '定稿上传': 'Final Version Upload',
    '定稿历史版本': 'Final Version History',

    // ocr
    'OCR图片识别': 'OCR Image recognition',

    // Object Storage
    '对象存储': 'Object Storage',
    '存储配置': 'Storage Configuration',
    '对象列表': 'Object List',

    // System Logs
    '系统日志': 'System Logs',
    '操作日志': 'Operation Logs',
    '登录日志': 'Login Logs',

    // System Management
    '系统管理': 'System Management',
    '菜单管理': 'Menu Management',
    '用户管理': 'User Management',
    '角色管理': 'Role Management',
    '字典管理': 'Dictionary Management',
    '部门管理': 'Department Management',
    '岗位管理': 'Position Management',
    '配置管理': 'Configuration Management',
    '代码生成': 'Code Generation',
    '定时任务': 'Scheduled Tasks',

    // version
    '版本管理': 'Version Management',
  },
  home: {
    mainTitle: 'AI Translation + Medical Large Model Integration',
    subTitle: 'AI Intelligent Translation Engine, Saving Your Precious Time',
    textTranslation: 'Text Translation',
    documentTranslation: 'Document Translation'
  },
  popVersion: {
    title: 'Version Update Notification',
    versionNumber: 'Version Number',
    updateTime: 'Update Time',
    updateContent: 'Update Content',
    cancel: 'Cancel',
    confirmRead: 'Confirm Read',
    markReadFailed: 'Failed to mark as read:'
  },
  docFile: {
    fileName: 'File Name',
    fileNamePlaceholder: 'Please enter file name',
    aiFileType: 'AI File Type',
    aiFileTypePlaceholder: 'Please enter AI file type',
    aiAnalysis: 'AI Analysis',
    statusPlaceholder: 'Please select status',
    createTime: 'Create Time',
    startDate: 'Start Date',
    endDate: 'End Date',
    fileSize: 'File Size',
    aiClassificationResult: 'AI Classification Result',
    aiQualityResult: 'AI Quality Result',
    reason: 'Reason',
    aiAnalysisCount: 'AI Analysis Count',
    approvalStatus: 'Approval Status',
    approved: 'Approved'
  },
  docAudit: {
    fileName: 'File Name',
    fileNamePlaceholder: 'Please enter file name',
    aiClassificationResult: 'AI Classification',
    aiClassificationPlaceholder: 'Please enter AI classification result',
    aiQualityResult: 'AI Quality',
    statusPlaceholder: 'Please select status',
    aiAnalysis: 'AI Analysis',
    errorReason: 'Error Reason',
    errorReasonPlaceholder: 'Please select error reason',
    createTime: 'Create Time',
    startDate: 'Start Date',
    endDate: 'End Date',
    batchDelete: 'Batch Delete',
    serialNumber: 'Number',
    fileSize: 'File Size',
    reason: 'Reason',
    aiAnalysisCount: 'AI Analysis Count',
    approvalStatus: 'Approval Status',
    pendingApproval: 'Pending Approval',
    analysisTime: 'Analysis Time',
    approve: 'Approve',
    selectAnalysisRecords: 'Please select records to analyze',
    aiAnalysisConfirm: 'Selected documents will be automatically classified and quality checked using AI technology. Continue?',
    submitSuccess: 'Submitted successfully, processing in progress, please refresh the list later',
    selectDeleteRecords: 'Please select records to delete',
    batchDeleteConfirm: 'Selected documents will be deleted and cannot be recovered. Continue?',
    deleteSuccess: 'Submitted successfully, deleting in progress, please refresh the list later',
    exportFileName: 'Task Approval List.xls'
  },
  auditDetail: {
    fileApproval: 'File Approval',
    fileAttributes: 'File Attributes',
    fileName: 'File Name',
    folderLocation: 'Folder Location',
    aiClassificationTip: 'AI Classification Tip',
    approvalProcess: 'Approval Process',
    demoApprovalProcess: 'Demo Approval Process',
    namingRules: 'Naming Rules',
    chineseNamingPrinciples: 'Chinese Drug Generic Name Naming Principles',
    fileVersion: 'File Version',
    schemeNumber: 'Scheme Number',
    projectName: 'Project Name',
    demoProject: 'Demo Project',
    countryName: 'Country Name',
    china: 'China',
    filePlan: 'File Plan',
    move: 'Move',
    approve: 'Approve',
    approvalOpinion: 'Approval Opinion',
    approvalResult: 'Approval Result',
    aiQualityTip: 'AI Quality Tip',
    qualityResult: 'Quality Result',
    approvalPassed: 'Approval Passed',
    approvalRejected: 'Approval Rejected',
    reason: 'Reason',
    selectRejectReason: 'Please select reject reason',
    rejectReason: 'Reject Reason',
    responsible: 'Responsible Person'
  },
  docExit: {
    fileName: 'File Name',
    fileNamePlaceholder: 'Please enter file name',
    aiFileType: 'AI File Type',
    aiFileTypePlaceholder: 'Please enter AI file type',
    aiAnalysis: 'AI Analysis',
    statusPlaceholder: 'Please select status',
    createTime: 'Create Time',
    startDate: 'Start Date',
    endDate: 'End Date',
    fileSize: 'File Size',
    aiClassificationResult: 'AI Classification Result',
    aiQualityResult: 'AI Quality Result',
    reason: 'Reason',
    aiAnalysisCount: 'AI Analysis Count',
    approvalStatus: 'Approval Status',
    rejected: 'Rejected'
  },
  docStatistics: {
    total: "Total Analysis",
    analysis: "Count in Analysis",
    success: "Successful Analysis",
    error: "Abnormal Analysis",
    pass: "Quality Inspection Pass",
    fail: "Quality Inspection Fail",
    uncertain: "Quality Inspection Uncertain",
    aiFileType: 'AI File Type',
    aiFileTypePlaceholder: 'Please enter AI file type',
    createTime: 'Create Time',
    startDate: 'Start Date',
    endDate: 'End Date',
    category: 'Category',
    fileCount: 'File Count',
    aiAnalysisCount: 'AI Analysis Count',
    qualityPassCount: 'Quality Pass Count'
  },
  docFileForm: {
    uploadFile: 'Upload File',
    dragOrClickUpload: 'Drag files here, or click to upload',
    pdfOnlyTip: 'Tip: Only .pdf format files are allowed!',
    pleaseUploadFile: 'Please upload file',
    aiAnalyzing: 'AI is analyzing at high speed...',
    uploadFailed: 'Upload failed, please upload again!',
    maxFilesExceeded: 'Maximum 1000 files can be uploaded!'
  },
}
