<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="主体单位" prop="entId">
        <el-input v-model="formData.entId" placeholder="请选择主体单位" />
      </el-form-item>
      <el-form-item label="消防部门" prop="deptId">
        <el-input v-model="formData.deptId" placeholder="请选择消防部门" />
      </el-form-item>
      <el-form-item label="维保人员(多人)" prop="staff">
        <el-input v-model="formData.staff" placeholder="请输入维保人员姓名" />
      </el-form-item>
      <el-form-item label="项目经理" prop="manager">
        <el-input v-model="formData.manager" placeholder="请输入项目经理姓名" />
      </el-form-item>
      <el-form-item label="维保描述" prop="serviceDesc">
        <el-input v-model="formData.serviceDesc" placeholder="请输入维保描述" />
      </el-form-item>
      <el-form-item label="维保图片列表" prop="serviceImg">
        <UploadImg v-model="formData.serviceImg" />
      </el-form-item>
      <el-form-item label="报告描述" prop="reportDesc">
        <el-input v-model="formData.reportDesc" placeholder="请输入报告描述" />
      </el-form-item>
      <el-form-item label="报告图片列表" prop="reportImg">
        <UploadImg v-model="formData.reportImg" />
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TaskRecordApi, TaskRecordVO } from '@/api/system/taskrecord'

/** 维保记录 表单 */
defineOptions({ name: 'TaskRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  entId: undefined,
  deptId: undefined,
  serviceDesc: undefined,
  serviceImg: undefined,
  reportDesc: undefined,
  reportImg: undefined,
  status: undefined,
  staff: undefined,
  manager: undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TaskRecordApi.getTaskRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TaskRecordVO
    if (formType.value === 'create') {
      await TaskRecordApi.createTaskRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await TaskRecordApi.updateTaskRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    entId: undefined,
    deptId: undefined,
    serviceDesc: undefined,
    serviceImg: undefined,
    reportDesc: undefined,
    reportImg: undefined,
    status: undefined,
    staff: undefined,
    manager: undefined,
  }
  formRef.value?.resetFields()
}
</script>