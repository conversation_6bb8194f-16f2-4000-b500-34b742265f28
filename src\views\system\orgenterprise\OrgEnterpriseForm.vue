<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="单位名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入单位名称" />
      </el-form-item>
      <el-form-item label="单位地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入单位地址" />
      </el-form-item>
      <el-form-item label="所属县区" prop="district">
        <el-input v-model="formData.district" placeholder="请输入所属县区" />
      </el-form-item>
      <el-form-item label="单位等级" prop="grade">
        <el-input v-model="formData.grade" placeholder="请输入单位等级" />
      </el-form-item>
      <el-form-item label="单位负责人" prop="leader">
        <el-input v-model="formData.leader" placeholder="请输入单位负责人" />
      </el-form-item>
      <el-form-item label="负责人联系方式" prop="leaderPhone">
        <el-input v-model="formData.leaderPhone" placeholder="请输入负责人联系方式" />
      </el-form-item>
      <el-form-item label="建筑面积" prop="buildSpace">
        <el-input v-model="formData.buildSpace" placeholder="请输入建筑面积" />
      </el-form-item>
      <el-form-item label="最后一次维保时间" prop="lastTime">
        <el-date-picker
          v-model="formData.lastTime"
          type="date"
          value-format="x"
          placeholder="选择最后一次维保时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { OrgEnterpriseApi, OrgEnterpriseVO } from '@/api/system/orgenterprise'

/** 单位底册 表单 */
defineOptions({ name: 'OrgEnterpriseForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  address: undefined,
  district: undefined,
  grade: undefined,
  leader: undefined,
  leaderPhone: undefined,
  buildSpace: undefined,
  lastTime: undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrgEnterpriseApi.getOrgEnterprise(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrgEnterpriseVO
    if (formType.value === 'create') {
      await OrgEnterpriseApi.createOrgEnterprise(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrgEnterpriseApi.updateOrgEnterprise(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    address: undefined,
    district: undefined,
    grade: undefined,
    leader: undefined,
    leaderPhone: undefined,
    buildSpace: undefined,
    lastTime: undefined,
  }
  formRef.value?.resetFields()
}
</script>