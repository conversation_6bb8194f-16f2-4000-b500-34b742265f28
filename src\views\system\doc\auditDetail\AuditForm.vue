<template>
  <Dialog v-model="dialogVisible" title="审批" width="600">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="审批结果:" prop="status">
        <el-select v-model="formData.status" placeholder="请选择结果" clearable style="width: 80%">
          <el-option
            v-for="dict in statusList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <span class="icon ai-btn">AI</span>
      </el-form-item>
      <el-form-item v-if="formData.status == 2" label="驳回原因:" prop="reason">

        <span class="icon ai-btn">AI</span>
      </el-form-item>
      <el-form-item v-if="formData.status == 2" label="命名规则:" prop="remark">
        <p class="info-content">《中国药品通用名称命名原则》</p>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" name="AuditForm" setup>
import { DocFileApi } from "@/api/system/doc";

const message = useMessage();
const dialogVisible = ref(false); // 弹窗的是否展示
const formLoading = ref(false); // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const statusList = [
  { label: "通过", value: 1 },
  { label: "驳回", value: 2 },
];
const formData = ref({
  id: 0,
  status: undefined,
  reason: "",
});
const formRules = reactive({
  status: [{ required: true, message: "审批结果不能为空", trigger: "blur" }],
  reason: [{ required: true, message: "驳回原因不能为空", trigger: "blur" }],
});
const formRef = ref(); // 表单 Ref
/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true;
  resetForm();
  formData.value.id = id;
};
defineExpose({ open }); // 提供 open 方法，用于打开弹窗
interface Emits {
  (event: "success", name: any): void;
}

/** 提交表单 */
const emit = defineEmits<Emits>(); // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return;
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  formLoading.value = true;
  try {
    const data = formData.value as unknown as TaskApi.TaskVO;
    await DocFileApi.updateDocFile(data);
    message.success("审核成功");
    dialogVisible.value = false;
    emit("success", formData.value.status);
  } finally {
    formLoading.value = false;
  }
};

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: 0,
    status: undefined,
    remark: "",
    deptId: undefined,
  };
  formRef.value?.resetFields();
};
</script>

<style lang="scss" scoped>
.ai-btn {
  display: inline-block;
  cursor: pointer;
  width: 30px;
  height: 30px;
  line-height: 24px;

  text-align: center;
  border: 3px solid #1989fa;
  color: #1989fa;
  border-radius: 4px;
  font-weight: 700;
  font-size: 14px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
  margin-left: 10px;

  &:hover {
    background: #1989fa;
    color: #fff;
  }
}
</style>
