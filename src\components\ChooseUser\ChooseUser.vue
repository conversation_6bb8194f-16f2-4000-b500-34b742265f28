<template>
  <div class="choose-user">
      <!-- 直接选人员 -->
      <el-select  v-model="userId" clearable style="width: 100%"  @change="setUserId" filterable>
        <el-option v-for="item in userList" :key="item.id" :label="item.nickname + '('+(item.deptName || '无')+')'" :value="item.id" />
      </el-select>
  </div>
</template>
<script lang="ts" setup>
import { defaultProps, handleTree } from "@/utils/tree";
import * as UserApi from "@/api/system/user";

defineOptions({ name: "ChooseDeptLeader" });

const userId = ref(""); // 用户ID
const userList = ref<any[]>([]); // 部门列表
 
const fetchData = async () => {
  // 获得全部人员
  userList.value = await UserApi.getSimpleUserList();
};


 onMounted(() => {
  fetchData();
 })

/** 提交表单 */
const emit = defineEmits(["setUserId"]); // 定义 success 事件，用于操作成功后的回调
 
const setUserId = (data) => {
  console.log(data)
  emit("setUserId", data)
};
 
</script>
<style lang="scss" scoped>
  .choose-user {
    min-width: 250px;
  }
</style>