import request from "@/config/axios";

// 文档信息 VO
export interface DocFileVO {
  id: number; // 主键ID
  fileName: string; // 文件名称
  fileUrl: string; // 文件地址
  fileSize: number; // 文件大小
  type: string; // AI文件类别
  status: number; // 审核状态
  result: string; // 质检结果
  reason: string; // 原因
  reasonList: any;
  startTime: string
  endTime: string// 原因
}

// 文档信息 API
export const DocFileApi = {
  // 查询文档信息分页
  getDocFilePage: async (params: any) => {
    return await request.get({ url: `/system/doc/page`, params });
  },

  // 查询文档信息详情
  getDocFile: async (id: number) => {
    return await request.get({ url: `/system/doc/get?id=` + id });
  },

  // 新增文档信息
  createDocFile: async (data: DocFileVO) => {
    return await request.post({ url: `/system/doc/create`, data });
  },

  // 修改文档信息
  updateDocFile: async (data: DocFileVO) => {
    return await request.put({ url: `/system/doc/update`, data });
  },

  // 删除文档信息
  deleteDocFile: async (id: number) => {
    return await request.delete({ url: `/system/doc/delete?id=` + id });
  },

  // 导出文档信息 Excel
  batchAiAnalysis: async (data: any) => {
    return await request.post({ url: "system/doc/batchAiAnalysis", data });
  },

  // 批量删除
  batchDelete: async (data: any) => {
    return await request.post({ url: "system/doc/batchDelete", data });
  },
  // 导出文档信息 Excel
  exportDocFile: async (params) => {
    return await request.download({ url: `/system/doc/export-excel`, params });
  },
  // 新增文档信息
  getDocStatistics: async (data: DocFileVO) => {
    return await request.post({ url: `/system/statistics/getDocStatistics`, data });
  },
};
// 导出文档信息 Excel
// 上传文件
export const updateFile = (data: any) => {
  return request.upload({ url: "/system/doc/upload", data });
};
