<template>
  <Dialog v-model="dialogVisible" title="审核">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item label="审核结果:" prop="auditStatus">
        <el-select v-model="formData.auditStatus" placeholder="请选择结果" clearable>
          <el-option v-for="dict in dictList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="理由:" prop="reason" v-if="formData.auditStatus == 3">
        <el-input v-model="formData.reason" :rows="3" type="textarea" placeholder="请输入理由" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" name="SystemDeptForm" setup>
import { DocTranslateApi } from "@/api/system/translate/index";

const dictList = ref([
  { label: "审核通过", value: 2 },
  { label: "审核不通过", value: 3 },
]);

const message = useMessage();
const dialogVisible = ref(false); // 弹窗的是否展示
const formLoading = ref(false); // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<ApplyAuditVO>({
  id: 0,
  auditStatus: null,
  reason: "",
});

const isRequired = computed(() => formData.value.auditStatus === 3);

const formRules = reactive({
  auditStatus: [{ required: true, message: "审核结果不能为空", trigger: "blur" }],
  reason: [{ required: isRequired, message: "填写理由", trigger: "blur" }],
});
const formRef = ref(); // 表单 Ref
/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true;
  resetForm();
  formData.value.id = id;
};
defineExpose({ open }); // 提供 open 方法，用于打开弹窗
interface Emits {
  (event: "success", name: any): void;
}

/** 提交表单 */
const emit = defineEmits<Emits>(); // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return;
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  formLoading.value = true;
  try {
    const data = (formData.value as unknown) as any;
    await DocTranslateApi.auditDocx(data);
    message.success("审核完成");
    dialogVisible.value = false;
    emit("success", formData.value.auditStatus);
  } finally {
    formLoading.value = false;
  }
};
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: 0,
    auditStatus: "",
    reason: "",
  };
  formRef.value?.resetFields();
};
</script>
