<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
      <el-form-item :label="t('translate.file.fileName')" prop="fileName">
        <el-input v-model="queryParams.fileName" :placeholder="t('translate.file.fileNamePlaceholder')" clearable
          @keyup.enter="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item :label="t('translate.file.translateType')" prop="type">
        <el-select v-model="queryParams.type" :placeholder="t('translate.file.selectType')" class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.TRANSFORM_TYPE)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="t('translate.file.translateStatus')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="t('translate.file.selectStatus')" class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRANSLATE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item :label="t('translate.file.createTime')" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          :start-placeholder="t('translate.file.startDate')" :end-placeholder="t('translate.file.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          {{ t("translate.file.search") }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          {{ t("translate.file.reset") }}
        </el-button>
        <el-button type="primary" plain @click="openForm" v-hasPermi="['system:translate:create']">
          <Icon icon="ep:plus" class="mr-5px" />
          {{ t("translate.file.uploadDoc") }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('translate.file.fileName')" align="left" prop="fileName" />
      <!-- <el-table-column :label="t('translate.file.fileUrl')" align="center" prop="fileUrlOrigin" /> -->
      <el-table-column :label="t('translate.file.fileSize')" align="center" prop="fileSize"
        :formatter="fileSizeFormatter" />
      <el-table-column :label="t('translate.file.translateType')" align="center" prop="type" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRANSFORM_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.file.translateStatus')" align="center" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRANSLATE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <!-- <el-table-column :label="t('translate.file.auditStatus')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.AUDIT_STATUS" :value="scope.row.auditStatus" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="t('translate.file.reason')" align="center" prop="reason" /> -->
      <el-table-column :label="t('translate.file.creator')" align="center" prop="createName" />
      <el-table-column :label="t('translate.file.createTime')" align="center" prop="createTime"
        :formatter="dateFormatter" width="180px" />
      <el-table-column :label="t('translate.file.translateTime')" align="center" prop="updateTime" width="180px">
        <template #default="scope">
          {{ scope.row.status == 0 ? "" : formatDate(scope.row.updateTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('translate.file.actions')" align="center" min-width="100px">
        <template #default="scope">
          <el-button v-hasPermi="['system:translate:query']" v-if="allowedViwer(scope.row.fileUrlTranslate)" link
            type="primary" @click="jumpTo(scope.row, 'watch')">
            {{ t("translate.file.view") }}
          </el-button>
          <!-- <el-button v-hasPermi="['system:translate:query']" link type="primary" @click="jumpTo(scope.row, 'editor')">
            {{ t('translate.file.edit') }}
          </el-button> -->
          <el-button v-hasPermi="['system:translate:share:create']" v-if="allowedViwer(scope.row.fileUrlTranslate)" link
            type="warning" @click="toShare(scope.row.id)">
            {{ t('translate.file.share') }}
          </el-button>
          <el-button v-hasPermi="['system:translate:download']" v-if="scope.row.fileUrlTranslate" link type="success"
            @click="toDownloadFile(scope.row.fileUrlTranslate, scope.row.fileName)">
            {{ t("translate.file.download") }}
          </el-button>
          <el-button v-hasPermi="['system:translate:delete']" link type="danger" @click="handleDelete(scope.row.id)">
            {{ t("translate.file.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>
  <fileImportForm ref="importFormRef" @success="getList" />
  <PdfDownload ref="pdfDownloadRef" @success="downloadSuccess" />
  <ShareForm ref="shareFormRef" />
</template>

<script setup lang="ts">
import { dateFormatter, formatDate } from "@/utils/formatTime";
import { DocTranslateApi, DocTranslateVO } from "@/api/system/translate";
import { fileSizeFormatter } from "@/utils";
import { DICT_TYPE, getIntDictOptions } from "@/utils/dict";
import download from "@/utils/download";
import PdfDownload from "@/components/DownloadTextFile/pdf.vue";
import fileImportForm from "./fileImportForm.vue";
import ShareForm from './shareForm.vue';

/** 文档翻译 */
defineOptions({ name: "TranslateFile" });
const router = useRouter();
const message = useMessage(); // 消息弹窗
const { t } = useI18n(); // 国际化

const loading = ref(true); // 列表的加载中
const list = ref<DocTranslateVO[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fileName: undefined,
  type: undefined,
  status: undefined,
  isAi: undefined,
  createTime: [],
});
const queryFormRef = ref(); // 搜索的表单
const exportLoading = ref(false); // 导出的加载中

// 允许预览的文件类型
const allowedFileTypes = getIntDictOptions(DICT_TYPE.ALLOW_VIEW_FILE_TYPE);

const getFileType = (fileUrl: string) => {
  if (!fileUrl) return '';
  return fileUrl.split("/")?.pop()?.split(".")[1];
};

const allowedViwer = computed(() => {
  return (url) => {
    if (!url) return false;
    const fileType = getFileType(url);
    return allowedFileTypes.some((item) => item.label === fileType);
  };
});

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await DocTranslateApi.getDocTranslatePage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields();
  handleQuery();
};

/** 添加/修改操作 */
const importFormRef = ref();
const openForm = () => {
  importFormRef.value.open();
};

// 分享
const shareFormRef = ref();
const toShare = (id: number) => {
  shareFormRef.value.open(id);
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    await DocTranslateApi.deleteDocTranslate(id);
    message.success(t("common.delSuccess"));
    // 刷新列表
    await getList();
  } catch { }
};

let intervalFn = null;
/** 初始化 **/
onMounted(() => {
  getList();
  intervalFn = setInterval(() => {
    getList();
  }, 10000);
});

onUnmounted(() => {
  clearInterval(intervalFn);
  intervalFn = null;
});
const jumpTo = (obj, type) => {
  if (obj.status === 0) {
    message.warning(t("translate.file.translating"));
    return;
  }
  if (obj.status === 2) {
    message.warning(t("translate.file.translateError"));
    return;
  }
  if (type === "editor") {
    router.push({
      path: "/translate/previewEditor",
      query: { id: obj.id },
    });
  } else {
    router.push({
      path: "/translate/preview",
      query: { id: obj.id },
    });
  }
};

const pdfDownloadRef = ref();
const downloadSuccess = () => {
  loading.value = false;
  message.success(t("translate.file.downloadSuccess"));
};

// // 下载pdf
// function toDownloadPDF(id: string) {
//   loading.value = true;
//   pdfDownloadRef.value.download(id);
// }

// 下载word
// async function toDownloadFile(id: string, fileName: string) {
//   loading.value = true;
//   try {
//     const data = await DocTranslateApi.downLoadDoc({ id });
//     const ext = fileName.split(".").pop()?.toLowerCase();
//     const curFileName = fileName.replace(`.${ext}`, `（译文）.${ext}`);
//     download.excel(data, curFileName);
//   } finally {
//     loading.value = false;
//   }
// }

// 下载文件
function toDownloadFile(fileUrl: string, fileName: string) {
  const fileNameStr = fileName.split(".").shift();
  const ext = fileUrl.split(".").pop()?.toLowerCase();
  const downLoadFileName = `${fileNameStr}（译文）.${ext}`;
  console.log("downLoadFileName", downLoadFileName);
  fetch(encodeURI(fileUrl)).then((res) => {
    res.blob().then((myBlob) => {
      const href = URL.createObjectURL(myBlob);
      const a = document.createElement("a");
      a.href = href;
      a.download = downLoadFileName; // 下载文件重命名，并指定文件扩展名为 ".docx"
      document.body.appendChild(a); // 将<a>元素添加到文档中，以便进行点击下载
      a.click();
      document.body.removeChild(a); // 下载完成后移除<a>元素
      URL.revokeObjectURL(href);
    });
  });
}
</script>

<style lang="scss" scoped></style>
