/**
 * 菜单国际化工具函数
 */

/**
 * 获取菜单的国际化键
 * @param menuName 菜单名称
 * @returns 国际化键
 */
export const getMenuI18nKey = (menuName: string): string => {
  return `menu.${menuName}`
}

/**
 * 获取菜单标题的翻译文本
 * @param titleKey 标题键
 * @param t 翻译函数
 * @returns 翻译后的文本
 */
export const getMenuTitle = (titleKey: string, t: (key: string) => string): string => {
  if (!titleKey) return ''
  const translatedTitle = t(titleKey)

  // 如果翻译键不存在，返回原始的菜单名称（去掉 menu. 前缀）
  if (translatedTitle === titleKey && titleKey.startsWith('menu.')) {
    return titleKey.replace('menu.', '')
  }

  return translatedTitle
}
