<template>
  <div class="choose-dept-user">
    <!-- 选部门下的人 -->
    <el-tree-select v-loading="treeLoading" v-model="userList" lazy :props="treeProps" multiple :data="deptList"
      :load="loadNode" :check-strictly="false" :render-after-expand="true" style="width: 100%" @change="setUserList" />
  </div>
</template>
<script lang="ts" setup>
import * as Dept<PERSON><PERSON> from "@/api/system/dept";
import { defaultProps, handleTree } from "@/utils/tree";
import * as UserApi from "@/api/system/user";

defineOptions({ name: "ChooseDeptUser" });

const userList = ref(""); // 用户ID
// const userList = ref<any[]>([]); // 用户列表
const deptList = ref<any[]>([]); // 部门列表
const treeProps = {
  label: "label",
  children: "childNodes",
  isLeaf: "isLeaf",
  value: "id",
  disabled: "disabled",
};
const initState = ref(false);
const curListUser = ref([]);
const loadNode = async (node, resolve) => {
  // 第一次返回空 因为这个是 两个接口 拼起来的
  if (!initState.value) {
    initState.value = true;
    return resolve([]);
  }
  if (!node.data.children && !node.isLeaf) {
    const data = await UserApi.getUserPage({
      pageNo: 1,
      pageSize: 100,
      deptId: node.data.id,
    });
    // 保存当前部门的用户
    const list = data.list.map((s) => {
      curListUser.value.push(s);
      return {
        id: s.id,
        label: s.nickname + '（' + s.mobile + '）',
        isLeaf: true,
        disabled: false,
      };
    });
    return resolve(list);
  }

  if (node.isLeaf) return resolve([]);

  return resolve(node.data.children || []);
};

// 选择
const treeLoading = ref(false);

// 获取部门列表
const fetchData = async () => {
  treeLoading.value = true;

  const data = await DeptApi.getDeptPage({
    pageNo: 1,
    pageSize: 100,
  });
  let dataList: any = [];

  // 人员
  data.forEach((s) => {
    s.label = s.name;
    s.isLeaf = false;
    s.disabled = false;
  });

  deptList.value = handleTree(data);
  treeLoading.value = false;
};

onMounted(() => {
  fetchData();
});

/** 提交表单 */
const emit = defineEmits(["setUserList"]); // 定义 success 事件，用于操作成功后的回调

const setUserList = (data) => {
  // let list = [];
  // if (data && data.length > 0) {
  //   data.forEach((s) => {
  //     curListUser.value.forEach((item) => {
  //       if (item.id === s) {
  //         list.push({ id: item.id, nickname: item.nickname });
  //         return true;
  //       }
  //       return false;
  //     });
  //   });
  // }
  // emit("setUserList", list);

  initState.value = false;
  emit("setUserList", data);
};
</script>
<style lang="scss" scoped>
.choose-dept-user {
  min-width: 250px;
}
</style>
