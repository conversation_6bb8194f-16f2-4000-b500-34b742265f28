<template>
  <div class="word-download-page">
    <div class="word-text">
      <div id="wordDom" class="text-content" v-html="fileContent"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import htmlDocx from "html-docx-fixed/dist/html-docx";
import { saveAs } from "file-saver";

const isShow = ref(false);
const emit = defineEmits(["success"]);
const fileContent = ref("");

const downloadFile = async (fileName: string, styledContent: string, uniqueClass: string) => {
  isShow.value = true;
  nextTick(async () => {
    try {
      // const htmlContent = fileContent.value;
      const pageContent = `<html>
        <head>
          <style>
            ${styledContent}
          </style>
        </head>
          <body>
            ${document.querySelector(`.${uniqueClass}`)?.innerHTML}
          </body>
        </html>
      `;

      // 使用 html-docx-js 转换 HTML 为 DOCX
      const docxBlob = htmlDocx.asBlob(pageContent);

      // 下载文件
      saveAs(docxBlob, fileName);
      emit("success", true); // 通知父组件下载完成
    } catch (error) {
      console.error("Word生成失败", error);
    }
    isShow.value = false;
  });
};

defineExpose({ downloadFile }); // 提供 open 方法，用于打开弹窗
</script>
<style lang="scss">
.word-download-page {
  position: fixed;
  top: 0;
  right: -100000px;
  bottom: 0;
  height: 100%;
  width: 847px;
  background: #fff;
}
#wordDom {
  // width: 594.3pt;
  // 594.3pt*840.51pt
  // height: 100%;
  // page-break-after: always; /* 每个块之后自动换页 */
  // position: relative;
  // width: 210mm; /* A4 页面宽度 */
  // height: 297mm; /* A4 页面高度 */
  // padding: 50px; /* 页边距 */
  box-sizing: border-box;
  overflow: hidden;
  page-break-before: avoid;
  // page-break-after: always;
  page-break-after: avoid; /* 最后一个块不换页 */

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 700;
    margin: 10px 0 20px 0;
    text-align: center;
    font-size: 22px;
    break-inside: avoid-column; /* 避免在列内断开 */
    page-break-inside: avoid; /* 避免在页面内断开 */
    column-break-inside: avoid; /* 避免在列内断开 */
  }
  p {
    line-height: 1.5;
    margin-bottom: 20px;
    font-size: 14px;
    break-inside: avoid-column; /* 避免在列内断开 */
    page-break-inside: avoid; /* 避免在页面内断开 */
    column-break-inside: avoid; /* 避免在列内断开 */

    &:last-child {
      page-break-after: avoid; /* 最后一个块不换页 */
      margin-bottom: 0px;
    }
  }
}

@page {
  p {
    &:last-child {
      page-break-after: avoid; /* 最后一个块不换页 */
      margin-bottom: 0px;
    }
  }
}
</style>
