import request from "@/config/axios";

// 文档翻译信息 VO
export interface DocTranslateVO {
  id: number; // 主键ID
  transId: string; // 翻译ID
  userIds: string; // 用户ID列表
  permission: number; // 权限标识 view、edit
  expireTime: string; // 过期时间
}

// 文档分享信息 API
export const ShareApi = {
  // 查询文档分享信息分页
  getSharePage: async (params: any) => {
    return await request.get({ url: `/system/share/page`, params });
  },

  // 查询文档分享信息详情
  getShare: async (id: number) => {
    return await request.get({ url: `/system/share/get?id=` + id });
  },

  // 新增文档分享信息
  createShare: async (data: VO) => {
    return await request.post({ url: `/system/share/create`, data });
  },

  // 修改文档分享信息
  updateShare: async (data: VO) => {
    return await request.put({ url: `/system/share/update`, data });
  },

  // 删除文档分享信息
  deleteShare: async (id: number) => {
    return await request.delete({ url: `/system/share/delete?id=` + id });
  },
};

