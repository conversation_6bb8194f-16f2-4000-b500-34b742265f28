import { ref, onUnmounted } from 'vue';
import mitt from 'mitt';

const emitter = mitt();

export function useEmit(eventName) {
  const emitData = ref(null);

  const emitEvent = (payload) => {
    emitter.emit(eventName, payload);
  };

  const onEmitEvent = (callback) => {
    emitter.on(eventName, callback);
  };

  const offEmitEvent = () => {
    emitter.off(eventName);
  };

  onUnmounted(offEmitEvent);

  return { emitEvent, onEmitEvent, emitData };
}
