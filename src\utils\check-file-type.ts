const word: readonly string[] = [
  '.doc', '.docm', '.docx', '.dot', '.dotm', '.dotx', '.epub', '.fb2', '.fodt', '.htm', '.html', '.mht', '.mhtml', '.odt', '.ott', '.pages', '.rtf', '.stw', '.sxw', '.txt', '.wps', '.wpt',
];

const cell: readonly string[] = [
  '.csv', '.et', '.ett', '.fods', '.numbers', '.ods', '.ots', '.sxc', '.xls', '.xlsb', '.xlsm', '.xlsx', '.xlt', '.xltm', '.xltx', '.xml',
]

const slide: readonly string[] = [
  '.dps', '.dpt', '.fodp', '.key', '.odp', '.otp', '.pot', '.potm', '.potx', '.pps', '.ppsm', '.ppsx', '.ppt', '.pptm', '.pptx', '.sxi',
]

const pdf: readonly string[] = [
  '.djvu', '.docxf', '.oform', '.oxps', '.pdf', '.xps',
];


export const checkFileType = (fileUrl: string): 'word' | 'cell' | 'slide' | 'pdf' | 'other' => {
  const ext = fileUrl.split('.').pop()?.toLowerCase()
  if (!ext) return 'other';
  if (word.includes(`.${ext}`)) return 'word';
  if (cell.includes(`.${ext}`)) return 'cell';
  if (slide.includes(`.${ext}`)) return 'slide';
  if (pdf.includes(`.${ext}`)) return 'pdf';
  return 'other';
}