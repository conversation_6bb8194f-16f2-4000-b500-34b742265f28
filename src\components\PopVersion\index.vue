<template>
  <div class="pop-version" v-if="visible">
    <div class="version-overlay"></div>
    <div class="version-card">
      <div class="version-header">
        <h2>{{ t('popVersion.title') }}</h2>
        <div class="version-close" @click="handleClose">&times;</div>
      </div>
      <div class="version-body">
        <div class="version-num">
          {{ t('popVersion.versionNumber') }}：{{ versionNum }}
        </div>
        <div class="version-time">
          {{ t('popVersion.updateTime') }}：{{ updateTime }}
        </div>
        <div class="version-title">
          {{ t('popVersion.updateContent') }}：{{ versionContent }}
        </div>
        <!-- <div class="version-content" v-html="versionContent"></div> -->
      </div>
      <div class="version-footer">
        <el-button plain text type="default" @click="handleClose">{{ t('popVersion.cancel') }}</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">{{ t('popVersion.confirmRead') }}</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime';
import { findLastVersion, readAppVersion } from '@/api/system/version/index';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const versionId = ref(null);
const versionContent = ref('');
const versionNum = ref('');
const updateTime = ref('');
const loading = ref(false);
const visible = ref(false);

const handleConfirm = async () => {
  if (!versionId.value) return;
  loading.value = true;
  try {
    await readAppVersion({ versionId: versionId.value });
    visible.value = false;
  } catch (error) {
    console.error(t('popVersion.markReadFailed'), error);
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  visible.value = false;
};

const fetchData = () => {
  findLastVersion().then(res => {
    versionId.value = res.id;
    versionContent.value = res.updateContent;
    versionNum.value = res.version;
    updateTime.value = formatDate(res.updateTime);
    if (res.status === 1 || res.status === null) {
      visible.value = true;
    }
  })
}

onMounted(() => {
  fetchData();
});

onActivated(() => {
  console.log(1)
  fetchData();
});

</script>
<style lang="scss" scoped>
.pop-version {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.version-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.version-card {
  position: relative;
  width: 600px;
  max-width: 90%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.version-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.version-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.version-close {
  font-size: 20px;
  cursor: pointer;
  color: #909399;
  transition: color 0.2s;
}

.version-close:hover {
  color: #f56c6c;
}

.version-body {
  padding: 20px;
  max-height: calc(80vh - 130px);
  overflow-y: auto;

  &>div {
    margin-bottom: 20px;
    color: #333;
  }
}

.version-content {
  line-height: 1.6;
  color: #606266;
  padding-left: 80px;
}

.version-footer {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

.version-footer .el-button {
  width: 120px;
}
</style>
