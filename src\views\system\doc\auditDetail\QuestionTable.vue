<template>
  <div class="question-table">
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="90px"
    >
      <el-form-item>
        <el-button
          type="primary"
          plain
          @click="handleAudit('create')"
          v-hasPermi="['system:doc:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" width="60px">
        <template #default="scope">
          {{ (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="质疑内容" align="center" prop="content" />
      <el-table-column label="质疑分类" align="center" prop="categary" />
      <el-table-column label="责任人" align="center" prop="user" />
      <el-table-column
        label="目标解决日期"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            type="primary"
            plain
            @click="handleAudit('update', scope.row.id)"
            v-hasPermi="['system:doc:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:doc:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
  <QueryForm ref="auditRef" @success="getList" />
</template>
<script lang="ts" name="AuditForm" setup>
import { DocFileApi } from "@/api/system/doc";
import { dateFormatter } from "@/utils/formatTime";
import QueryForm from "./QueryForm.vue";

const router = useRouter();
const message = useMessage(); // 消息弹窗

const loading = ref(true); // 列表的加载中
const list = ref<any[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
});

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await DocFileApi.getDocFilePage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    // await DocFileApi.deleteDocFile(id)
    message.success("删除成功");
    // 刷新列表
    await getList();
  } catch {}
};

/** 添加/修改操作 */
const auditRef = ref();
const handleAudit = (type: string, id: number | string = "") => {
  auditRef.value.open(id);
};

onMounted(() => {
  getList();
});
</script>
