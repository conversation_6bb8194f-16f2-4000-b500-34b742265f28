<template>
  <div
    :class="prefixCls"
    class="h-[100%] relative <xl:bg-v-dark <sm:px-10px <xl:px-10px <md:px-10px"
  >
    <div
      class="h-full flex flex-col items-center m-auto w-[100%] @2xl:max-w-400px @xl:max-w-400px @md:max-w-400px @lg:max-w-400px"
    >
      <!-- 账号登录 -->
      <AuthLoginForm
        v-if="isAuthLogin"
        class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
      />
      <LoginForm v-else class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)" />
    </div>
  </div>
</template>
<script lang="ts" name="Login" setup>
import { useDesign } from "@/hooks/web/useDesign";
import { useAppStore } from "@/store/modules/app";
import { underlineToHump } from "@/utils";
import { LoginForm, AuthLoginForm } from "./components";
import { getTokenByCode } from "@/api/login";
import * as authUtil from "@/utils/auth";
import { useRouter } from "vue-router";

const router = useRouter();
const appStore = useAppStore();
const { getPrefixCls } = useDesign();
const prefixCls = getPrefixCls("login");
const isAuthLogin = ref(false);
const { search } = window.location;

const parseQuery = (query: string) => {
  return query
    .replace("?", "")
    .split("&")
    .reduce((acc, cur) => {
      const [key, value] = cur.split("=");
      acc[underlineToHump(key)] = value;
      return acc;
    }, {});
};

if (search && search.includes("code")) {
  isAuthLogin.value = true;
  // 去掉参数
  var url = new URL(window.location.href);
  url.search = ""; // 将搜索部分设置为空字符串，即移除所有查询参数
  window.history.replaceState({}, document.title, url.href);
  // 获取code
  const { code, state } = parseQuery(search);
  getTokenByCode({ code }).then((res) => {
    authUtil.setToken(res);
    router.push({ path: "/" });
  });
}
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-login;
.relative {
  background-image: url("@/assets/imgs/home.jpg");
  background-size: cover;
}
</style>
