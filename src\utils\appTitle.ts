/**
 * 应用标题国际化工具
 */
import { watch } from 'vue'
import { i18n } from '@/plugins/vueI18n'
import { useAppStore } from '@/store/modules/app'

/**
 * 获取国际化的应用标题
 * @returns 国际化后的应用标题
 */
export const getAppTitle = (): string => {
  try {
    return i18n.global.t('app.title')
  } catch {
    // 如果国际化未初始化，返回环境变量中的默认标题
    return import.meta.env.VITE_APP_TITLE || '恒瑞大模型平台'
  }
}

/**
 * 更新页面标题
 * @param pageTitle 页面标题（可选）
 */
export const updatePageTitle = (pageTitle?: string) => {
  const appTitle = getAppTitle()
  const title = pageTitle ? `${appTitle} - ${pageTitle}` : appTitle
  
  if (document) {
    document.title = title
  }
}

/**
 * 监听语言变化并更新应用标题
 */
export const watchLanguageChange = () => {
  // 确保 i18n 已经初始化
  if (!i18n || !i18n.global) {
    console.warn('i18n not initialized yet')
    return
  }

  // 监听语言变化
  watch(
    () => i18n.global.locale.value,
    () => {
      // 语言变化时更新页面标题
      updatePageTitle()
    },
    { immediate: true }
  )
}
