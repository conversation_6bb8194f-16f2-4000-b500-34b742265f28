<template>
  <!-- <button class="" @click="callCommand">callCommand</button> -->
  <div :id="props.officeIdName"></div>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, ref } from "vue";
import { DocumentEditor } from "@onlyoffice/document-editor-vue";
import { checkFileType } from "@/utils/check-file-type";
// import { generateUUID } from "@/utils";
// import md5 from "js-md5";

const props = defineProps<{
  officeIdName: string; // 容器的类名
  updateTime: number; // 更新时间
  fileUrl: string; // 文档的 URL
  fileName: string; // 文档标题
  userId?: string; // 用户 ID（可选）
  userName?: string; // 用户名（可选）
  userEmail?: string; // 用户邮箱（可选）
  callbackUrl?: string; // 回调 URL（可选）
}>();


let docEditor: any;
const onRequestSelectDocument = (data) => {
  console.log(data);
  // docEditor.setRequestedDocument({
  //   c: event.data.c,
  //   fileType: "docx",
  //   url: "https://example.com/url-to-example-document.docx",
  //   token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmaWxlVHlwZSI6ImRvY3giLCJ1cmwiOiJodHRwczovL2V4YW1wbGUuY29tL3VybC10by1leGFtcGxlLWRvY3VtZW50LmRvY3gifQ.t8660n_GmxJIppxcwkr_mUxmXYtE8cg-jF2cTLMtuk8",
  // })
};

const onRequestHistory = (data) => {
  console.log(data);
};

let connector: any;
window.Asc = { plugin: null };
const onDocumentReady = (data) => {
  console.log(data);
  console.log(docEditor);
  // console.log(docEditor.instances)
  // console.log(docEditor.instances.docEditor)

  connector = docEditor.createConnector();
  console.log(connector);
  connector.attachEvent("onClickParagraph", (obj) => {
    console.log(`[EVENT] onClickParagraph: ${JSON.stringify(obj)}`);
  });

  // window.connector = docEditor.instances.docEditor.createConnector();
  window.Asc.plugin.init = function init() {
    // this.callCommand(() => {
    //   const oDocument = Api.GetDocument()
    //   const oParagraph = Api.CreateParagraph()
    //   oParagraph.AddText("Hello world!")
    //   oDocument.InsertContent([oParagraph])
    // }, true)
  };
  window.Asc.plugin.button = function button(id) {};
};


const documentConfig = ref({
  cache: false,
  type: "desktop",
  documentType: checkFileType(props.fileUrl), // 根据文件扩展名确定文档类型
  mode: "edit", //view为只能浏览  edit为编辑 review为审阅
  document: {
    title: props.fileName,
    url: props.fileUrl,
    fileType: props.fileUrl.split(".").pop()?.toLowerCase(),
    key: props.officeIdName + "_" + props.updateTime,
    permissions: {
      print: false,
      download: true,
    },
  },
  editorConfig: {
    // callbackUrl: "https://www.onlyoffice.com:443/callback.ashx?from=office-suite", // 使用传入的回调 URL
    callbackUrl: "http://36.156.124.58:8050/admin-api/system/translate/callback", // 使用传入的回调 URL
    // callbackUrl: props.callbackUrl || "http://baidu.com", // 使用传入的回调 URL
    user: {
      id: props.userId || "1", // 使用传入的用户 ID，默认为 "1"
      name: props.userName || "Anonymous", // 使用传入的用户名，默认为 "admin"
      email: props.userEmail || "<EMAIL>", // 使用传入的用户邮箱，默认为 "<EMAIL>"
    },
    //   // 编辑器常规配置
    customization: {
      // 自动保存可以关闭，常规ctrl+s更好用
      autosave: true,
      anonymous: {
        request: true,
        label: "Guest",
      },
      forcesave: true,
      help: false,
      about: false,

      hideNotes: true,
      hideRightMenu: true,
      hideRulers: true,

      macros: false,
      comments: false,
      mentionShare: false,
      compactHeader: true,
      compactToolbar: true,
      compatibleFeatures: true,
      toolbarHideFileName: true,

      feedback: false,
      plugins: false,
      layout: {
        header: {
          editMode: false,
          save: false,
          users: false,
        },
        leftMenu: {
          mode: false,
          navigation: true,
          spellcheck: true,
        },
        rightMenu: {
          mode: false,
        },
        statusBar: false,
        toolbar: {
          collaboration: {
            mailmerge: false,
          },
          draw: false,
          file: {
            close: true,
            info: true,
            save: true,
            settings: true,
          },
          home: {},
          layout: true,
          plugins: false,
          data: false,
          protect: false,
          references: true,
          save: true,
          view: {
            navigation: true,
          },
        },
      },

      logo: {
        // logo配置
        image: "http://36.156.124.58:8051/assets/logo-D6YdCz87.png",
        imageDark: "http://36.156.124.58:8051/assets/logo-D6YdCz87.png",
        url: "http://36.156.124.58:8051",
        visible: false,
      },
      features: {
        roles: false,
        spellcheck: {
          mode: false,
          change: false,
        },
        tabBackground: {
          mode: "toolbar",
          change: true,
        },
        tabStyle: {
          mode: "line",
          change: true,
        },
      },
      // review: {
      //   hideReviewDisplay: true,
      //   showReviewChanges: false,
      //   reviewDisplay: "edit",
      //   trackChanges: true,
      //   hoverMode: false,
      // },
    },
    lang: "zh",
    //   // token:  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjIjoiYWRkIiwiaW1hZ2VzIjpbeyJmaWxlVHlwZSI6InBuZyIsInVybCI6Imh0dHBzOi8vZXhhbXBsZS5jb20vdXJsLXRvLWV4YW1wbGUtaW1hZ2UxLnBuZyJ9LHsiZmlsZVR5cGUiOiJwbmciLCJ1cmwiOiJodHRwczovL2V4YW1wbGUuY29tL3VybC10by1leGFtcGxlLWltYWdlMi5wbmcifV19.JfSa__qPeY3MjUgdkJDjdfJWBgvCmEdLfFzjd3WgeUA",
  },
  events: {
    onRequestSelectDocument,
    onRequestHistory,
    onDocumentReady,
  },
});

const onlyOfficeScript = ref<HTMLScriptElement | null>(null);

onMounted(() => {
  // 加载 OnlyOffice SDK
  if (!onlyOfficeScript.value) {
    const script = document.createElement("script");
    script.src = "http://*************:9898/web-apps/apps/api/documents/api.js";
    // script.src = "http://***********:10100/web-apps/apps/api/documents/api.js";
    script.onload = () => {
      nextTick(() => {
        initializeEditor();
      });
    };
    document.head.appendChild(script);
    onlyOfficeScript.value = script;
  } else {
    initializeEditor();
  }
});


const callCommand = () => {
  window.Asc.scope = {};
  window.connector.callCommand(
    function () {
      console.log(window);
      console.log(editor);
      var oDocument = Api.GetDocument();
      var oRange = oDocument.GetRangeBySelect();
      if (!oRange) {
        return;
      }
      oRange.AddText("点击了文档");
      oRange.SetHighlight("yellow");
    },
    function (data) {
      console.log("callback command");
    }
  );
};

const initializeEditor = () => {
  console.log("initializeEditor");
  console.log("key: " + props.officeIdName + "_" + props.updateTime);
  // console.log(props.officeIdName);
  // console.log(props.fileUrl);
  docEditor = new (window as any).DocsAPI.DocEditor(props.officeIdName, documentConfig.value);
  // docEditor.render();
  // const connector = docEditor.createConnector();
  // console.log(connector)
  // connector.attachEvent("onChangeContentControl", (obj) => {
  //   console.log(`[EVENT] onChangeContentControl: ${JSON.stringify(obj)}`);
  // });
};
</script>

<style scoped>
#onlyoffice-editor {
  width: 100%;
  height: 600px; /* 根据需要调整高度 */
}

.extra #header-logo.logo-light i {
  background: url("../../assets/logo.png") center / cover no-repeat !important;
}
</style>
