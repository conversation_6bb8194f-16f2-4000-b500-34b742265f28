<template>
  <div class="pdf-preview" v-loading="state.loading">
    <div class="pdf-wrap"  :style="{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: `translate(-50%,-50%) scale(${state.scale})`,
        width: `${pageWidth}`,
        height: `${pageHeight}`,
    }">
      <vue-pdf-embed
        :page="state.pageNum"
        :source="state.source"
        class="vue-pdf-embed"
      />
    </div>
    <div class="page-tool">
      <div class="page-tool-item" @click="lastPage">上一页</div>
      <div class="page-tool-item" @click="nextPage">下一页</div>
      <div class="page-tool-item">
        {{ state.pageNum }}/{{ state.numPages }}
      </div>
      <div class="page-tool-item" @click="pageZoomOut">放大</div>
      <div class="page-tool-item" @click="pageZoomIn">缩小</div>
      <div class="page-tool-item" @click="pageRest">重置缩放</div>
      <div class="page-tool-item" @click="downloadPDF">下载</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { reactive, onMounted, computed } from "vue";
import VuePdfEmbed from "vue-pdf-embed";
import { createLoadingTask } from "vue3-pdfjs";

const props = defineProps({
  pdfUrl: {
    type: String,
    required: true,
  },
  pdfName: {
    type: String,
    required: true,
  },
});

const state = reactive({
  source: props.pdfUrl, // 预览pdf文件地址
  pageNum: 1, //当前页面
  scale: 1, // 缩放比例
  numPages: 0, // 总页数
  loading: "",//加载效果
});
// 下载pdf
function downloadPDF() {
  fetch(encodeURI(props.pdfUrl)).then((res) => {
    res.blob().then((myBlob) => {
      const href = URL.createObjectURL(myBlob);
      const a = document.createElement("a");
      a.href = href;
      a.download = props.pdfName; // 下载文件重命名，并指定文件扩展名为 ".pdf"
      document.body.appendChild(a); // 将<a>元素添加到文档中，以便进行点击下载
      a.click();
      document.body.removeChild(a); // 下载完成后移除<a>元素
    });
  });
}

onMounted(() => {
  const loadingTask = createLoadingTask({
    url: props.pdfUrl,
    withCredentials: true,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
  });
  // const loadingTask = createLoadingTask(props.pdfUrl);
  state.loading = true; // 添加一个loading状态
  loadingTask.promise.then((pdf: { numPages: number }) => {
    state.numPages = pdf.numPages;
    state.loading = false; // 加载完成后将loading状态设置为false
  });
});

const pageHeight = ref('100%');
const pageWidth = ref('100%');
function lastPage() {
  if (state.pageNum > 1) {
    state.pageNum -= 1;
  }
}

function nextPage() {
  if (state.pageNum < state.numPages) {
    state.pageNum += 1;
  }
}

function pageZoomOut() {
  if (state.scale < 2) {
    state.scale += 0.1;
    pageHeight.value = (parseInt(pageHeight.value) - 5.0) + '%';
    pageWidth.value = (parseInt(pageWidth.value) - 5.0) + '%';
  }
}

function pageZoomIn() {
  if (state.scale > 1) {
    state.scale -= 0.1;
    pageHeight.value = (parseInt(pageHeight.value) + 5.0) + '%';
    pageWidth.value = (parseInt(pageWidth.value) + 5.0) + '%';
  }
}

const pageRest = () => {
  state.scale = 1;
  pageHeight.value = '100%';
  pageWidth.value = '100%';
}


</script>
<style lang="scss" scoped>
.pdf-preview {
  position: relative;
  height: 100%;
  min-height: 800px;
  padding: 20px 0;
  box-sizing: border-box;
  background-color: #e9e9e9;
  overflow: auto;
}

.pdf-wrap {
  // overflow: auto;
}

.vue-pdf-embed {
  text-align: center;
  width: 515px;
  border: 1px solid #e5e5e5;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-tool {
  position: fixed;
  top: 950px;
  left: 100px;
  margin-left: 240px;
  padding-left: 15px;
  padding-right: 15px;
  display: flex;
  align-items: center;
  background: rgb(66, 66, 66);
  color: white;
  border-radius: 19px;
  z-index: 100;
  cursor: pointer;
  user-select: none;
  min-width: 490px;
}

.page-tool-item {
  padding: 8px 15px;
  padding-left: 10px;
  cursor: pointer;

  &:hover, &:focus, &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
</style>

