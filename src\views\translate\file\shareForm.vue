<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :width="800" :close-on-click-modal="false">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.file.acceptShareUser')" prop="userIds" required>
            <el-col :span="16">
              <el-input v-model="formData.userNames" :placeholder="t('translate.file.selectShareUserPlaceholder')" readonly />
              <!-- <choose-dept-user @set-user-list="setUserList" /> -->
            </el-col>
            <el-col :span="1"></el-col>
            <el-col :span="4">
              <el-button type="primary" @click="toShowUserPage">{{ t('translate.file.clickToSelect') }}</el-button>
            </el-col>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.file.docPermission')" prop="permission" required>
            <el-radio-group v-model="formData.permission">
              <el-radio label="view">{{ t("translate.file.view") }}</el-radio>
              <el-radio label="edit">{{ t("translate.file.edit") }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('translate.file.expireTime')" prop="expireTime" required>
            <el-date-picker v-model="formData.expireTime" type="date" :placeholder="t('translate.file.expireTime')" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">{{ t("common.submit") }}</el-button>
      <el-button @click="close">{{ t("common.cancel") }}</el-button>
    </template>

    <ChooseUserPageMutiple ref="ChooseDeptUserMutipleRef" @set-user-list="setUserList" />
  </Dialog>
</template>
<script lang="ts" setup>
import { FormRules } from "element-plus";
import { ShareApi } from "@/api/system/share";
import { useI18n } from "vue-i18n";
import { useMessage } from "@/hooks/web/useMessage";
import { useRouter } from "vue-router";
import ChooseUserPageMutiple from "@/components/ChooseUser/ChooseUserPageMutiple.vue";

defineOptions({ name: "ShareForm" });
const { t } = useI18n();
const message = useMessage(); // 消息弹窗

const dialogVisible = ref(false); // 弹窗的是否展示
const dialogTitle = ref(computed(() => t('translate.file.shareFormTitle'))); // 弹窗的标题
const formLoading = ref(false);
const formData = ref({
  transId: "",
  userNames: "",
  userIds: "",
  permission: "", // eadit、view
  expireTime: "",
});

// 选择人员
const ChooseDeptUserMutipleRef = ref();
const toShowUserPage = () => {
  ChooseDeptUserMutipleRef.value.open();
};

const setUserList = (ids: number[], names: string) => {
  formData.value.userIds = ids;
  formData.value.userNames = names;
};

const formRules = reactive<FormRules>({
  userIds: [
    {
      required: true,
      message: computed(() => t('translate.file.selectShareUserRequired')),
      trigger: "blur",
    },
  ],
  permission: [
    {
      required: true,
      message: computed(() => t('translate.file.selectDocPermissionRequired')),
      trigger: "blur",
    },
  ],
  expireTime: [
    {
      required: true,
      message: computed(() => t('translate.file.selectExpireTimeRequired')),
      trigger: "change",
    },
  ],
});
const formRef = ref(); // 表单 Ref
const router = useRouter();
// const curId = computed(() => router.currentRoute.value.query?.id);
/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true;
  formData.value.transId = id;
};

const close = () => {
  formData.value = { transId: "", userNames: "", userIds: "", permission: "", expireTime: "" };
  dialogVisible.value = false;
};

defineExpose({ open, close }); // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(["success"]); // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return;
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  formLoading.value = true;
  try {
    const data: any = formData.value;
    if (data.id) {
      await ShareApi.updateShare(data);
    } else {
      await ShareApi.createShare(data);
    }
    message.success(t('translate.file.submitSuccess'));
    close();
    // 发送操作成功的事件
    // emit("success", props.whichPannel === "left" ? data.originChange : data.translationChange);
    emit("success");
  } finally {
    formLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
.origin-text {
  color: #999;
  line-height: 20px;
  font-size: 14px;
  border: 1px solid #eee;
  padding: 5px 11px;
  border-radius: 4px;
}
</style>
