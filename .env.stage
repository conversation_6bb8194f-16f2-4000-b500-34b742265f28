# 预生产环境
NODE_ENV=stage

VITE_DEV=false

# 请求路径
# VITE_BASE_URL='http://192.168.0.203:8051'
VITE_BASE_URL='http://36.156.124.58:8051'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server
# 上传路径
VITE_UPLOAD_URL='/admin-api/infra/file/upload'

# 上传路径
VITE_UPLOAD_DOC_URL='/admin-api/system/doc/upload'

# 上传路径
VITE_UPLOAD_TRS_URL='/admin-api/system/translate/upload'

# onlyoffice服务地址
# VITE_ONLY_OFFICE_BASE_URL='http://192.168.0.169:9898'
VITE_ONLY_OFFICE_BASE_URL='http://36.156.124.58:9898'

#  onlyoffice回调地址
# VITE_ONLY_OFFICE_CALLBACK_URL='http://192.168.0.203:8051/admin-api/system/translate/callback'
VITE_ONLY_OFFICE_CALLBACK_URL='http://36.156.124.58:8051/admin-api/system/translate/callback'

# 接口地址前缀
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist-prod

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://mall.yudao.iocoder.cn'
