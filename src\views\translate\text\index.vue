<template>
  <div class="translate-box">
    <div class="translate-text">
      <div class="select-box">
        <div class="select-language">
          <p class="select-language-left">{{ sLang }}</p>
          <!--          <p class="select-language-center" @click="handleSwitch">-->
          <p class="select-language-center">
            <Icon :size="16" color="#1c66d9" icon="ep:switch" />
          </p>
          <p class="select-language-right">{{ tLang }}</p>
        </div>

        <div class="select-switch">
          <p class="switch-name">{{ t("translate.text.translateScene") }}</p>
          <el-select v-model="formData.scene" :placeholder="t('translate.text.selectTranslateScene')" class="!w-150px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.SCENARIO)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
          <p class="switch-name">{{ t("translate.text.enableCache") }}</p>
          <el-switch v-model="formData.isCache" />
        </div>
      </div>
      <div class="translate-wrap">
        <div class="translate-left">
          <el-input class="source-lang" v-model="sLangText" resize="none" type="textarea"
            :placeholder="t('translate.text.inputTextToTranslate')" @keyup="handleTranslate" />
          <Icon v-if="sLangText.length > 0" @click="handleClear" class="icon-clear" :size="20" color="#999"
            icon="ep:close" />
        </div>
        <div class="translate-right">
          <el-input class="translation" v-model="tLangText" resize="none" type="textarea"
            :placeholder="t('translate.text.translation')" readonly />
          <el-button class="copy-btn" type="primary" v-if="tLangText" @click="handleCopy" :icon="CopyDocument">{{
            t("translate.text.copy") }}</el-button>
        </div>

        <!-- <div class="translate-history" v-if="showHistory">
          <div class="history-title">
            <p class="text">{{ t('translate.text.translateHistory') }}</p>
            <p class="refresh">
              <Icon @click="toRefresh" :size="18" icon="ep:refresh" />
            </p>
          </div>
          <div class="history-item-box" v-loading="historyParams.loadding">
            <div class="history-item" v-for="(item, index) in translateHistory" :key="item.id"
              @click="handleHistoryItemClick(item)">
              <div class="history-item-origin">
                <p class="text">{{ item.origin }}</p>
                <p class="delete" @click.stop="deleteHistoryItem(item, index)">
                  <icon icon="ep:delete" />
                </p>
              </div>
              <div class="history-item-translate">{{ item.translate }}</div>
            </div>
          </div>
          <el-pagination v-show="historyParams.total > 0" v-model:current-page="historyParams.pageNo"
            v-model:page-size="historyParams.pageSize" :total="historyParams.total" small
            class="text-center mt-15px mb-15px ml-auto mr-auto" layout="total, prev, pager, next"
            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div> -->
        <!-- <div class="toggle-btn-box" @click="toggleShowHistory">
          <Icon icon="ep:d-arrow-right" v-if="showHistory"></Icon>
          <Icon icon="ep:d-arrow-left" v-else></Icon>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="TranslateText">
import { CopyDocument } from "@element-plus/icons-vue";
import { useDebounceFn } from "@vueuse/core";
import { DocTranslateApi } from "@/api/system/translate";
import { DICT_TYPE, getIntDictOptions } from "@/utils/dict";
import { copyTextToClipboard } from "@/utils/index";
import { useI18n } from "vue-i18n";
/** 文本翻译 */
defineOptions({ name: "TranslateText" });

const { t } = useI18n();
const message = useMessage(); // 消息弹窗

const loading = ref(true); // 列表的加载中
const formData = ref({
  from: "zh",
  to: "en",
  content: "",
  isCache: true,
  scene: 1,
  type: 1, // type  1：中翻英  2：英翻中
  source: 1, // 1：本系统   2：argus系统  3：App
});
const sLang = ref(computed(() => t("translate.text.chineseSimplified")));
const tLang = ref(computed(() => t("translate.text.english")));
const handleSwitch = () => {
  [sLang.value, tLang.value] = [tLang.value, sLang.value];
  if (sLang.value === t("translate.text.chineseSimplified")) {
    formData.value.from = "zh";
    formData.value.to = "en";
  } else {
    formData.value.from = "en";
    formData.value.to = "zh";
  }
  sLangText.value = tLangText.value.trim();
  tLangText.value = "";
  formData.value.content = sLangText.value.trim();
  toTranslate();
};

const sLangText = ref("");
const tLangText = ref("");

const handleClear = () => {
  sLangText.value = "";
  tLangText.value = "";
};
// 自动翻译 防抖
const handleTranslate = useDebounceFn(() => {
  if (sLangText.value === "" || sLangText.value.trim() === "") {
    handleClear();
    return;
  }
  formData.value.content = sLangText.value;
  toTranslate();
}, 400);

const toTranslate = () => {
  let text = sLangText.value.trim();
  if (text) {
    tLangText.value = t("translate.text.translating");
    DocTranslateApi.textTranslate(formData.value).then((res) => {
      tLangText.value = res?.content || '';
    });
  }
};

const handleCopy = () => {
  copyTextToClipboard(tLangText.value);
  message.success(t("translate.text.copySuccess"));
};

// const translateHistory = ref<any>([]);
// const historyParams = reactive({
//   pageNo: 1,
//   pageSize: 10,
//   total: 0,
//   loadding: false,
// });
// const fetchTranslateHistory = () => {
//   historyParams.loadding = true;
//   DocTranslateApi.getRecordTranslatePage({
//     pageNo: historyParams.pageNo,
//     pageSize: historyParams.pageSize,
//   }).then((res) => {
//     translateHistory.value = res.list;
//     historyParams.total = res.total;
//     historyParams.loadding = false;
//   }).catch(err => {
//     historyParams.loadding = false;
//   });
// };

// const showHistory = ref(false);
// const toggleShowHistory = () => {
//   showHistory.value = !showHistory.value;
// };

// const toRefresh = () => {
//   historyParams.pageNo = 1;
//   fetchTranslateHistory();
// }

// const handleSizeChange = (val: number) => {
//   historyParams.pageSize = val;
//   toRefresh();
// };

// const handleCurrentChange = (val: number) => {
//   historyParams.pageNo = val;
//   fetchTranslateHistory();
// };

// const handleHistoryItemClick = (item: any) => {
//   const { origin, translate, scene } = item;
//   formData.value.content = sLangText.value = origin;
//   formData.value.translate = tLangText.value = translate;
//   formData.value.scene = scene;
// };

// const deleteHistoryItem = (item: any, index: number) => {
//   ElMessageBox.confirm(t('translate.text.deleteTip'), t('common.tip'), {
//     confirmButtonText: t('common.ok'),
//     cancelButtonText: t('common.cancel'),
//     type: "warning",
//   }).then(() => {
//     DocTranslateApi.deleteRecordTranslate(item.id).then(() => {
//       translateHistory.value.splice(index, 1);
//     });
//   });
// };

// /** 初始化 **/
// onMounted(() => {
//   fetchTranslateHistory();
// });
</script>

<style lang="scss" scoped>
.translate-box {
  width: 100%;
  height: 100%;
  color: #1d2129;
  background: #fff;
  border: 1px solid #f2f3f5;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgb(35 46 67 / 6%);

  .translate-text {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .select-box {
      display: flex;
      width: 100%;
      padding: 10px 20px;
      justify-content: space-between;
      align-items: center;

      .select-switch {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;

        .switch-name {
          min-width: 100px;
          margin: 0 10px 0 50px;
          text-align: right;
        }
      }

      .select-language {
        display: flex;
        width: 230px;
        line-height: 40px;
        justify-content: space-between;

        .select-language-left,
        .select-language-right {
          font-size: 14px;
          color: #333;
        }

        .select-language-center {
          display: flex;
          width: 40px;
          height: 40px;
          text-align: center;
          cursor: pointer;
          border-radius: 50%;
          flex: 0 0 40px;
          align-items: center;
          justify-content: center;

          &:hover {
            background: #f5f7fa;
          }
        }
      }
    }

    .translate-wrap {
      display: flex;
      width: 100%;
      height: 600px;
      border-top: 1px solid #f2f3f5;
      justify-content: space-between;
      position: relative;

      .translate-left,
      .translate-right {
        position: relative;
        height: 100%;
        flex: 1;
      }

      .toggle-btn-box {
        height: 42px;
        width: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        // border-left: 1px dashed #f2f3f5;
        text-align: center;
        user-select: none;

        &:hover {
          color: #409eff;
        }
      }

      .translate-history {
        width: 300px;
        display: flex;
        flex-direction: column;
        position: relative;



        .history-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 20px;

          .text {
            font-size: 16px;
            font-weight: 700;
          }

          .refresh {
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            margin-right: 20px;

            &:hover {
              i {
                color: #409eff;
              }
            }
          }
        }

        .history-item-box {
          flex: 1;
          overflow-y: auto;
        }

        .history-item {
          padding: 10px;
          margin: 0 10px;
          border-top: 1px dashed #f2f3f5;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background: rgba(0, 0, 0, 0.05);

            .history-item-origin {
              color: #409eff;

              .delete {
                display: flex;
                background: #ed6264;

                i {
                  color: #fff;
                }
              }
            }

            .history-item-translate {
              color: #93c9ff;
            }
          }

          .history-item-origin {
            font-size: 16px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
            display: flex;
            align-content: center;
            position: relative;

            .text {
              flex: 1;
              padding-right: 30px;
            }

            .delete {
              position: absolute;
              right: 0px;
              top: 0px;
              display: none;
              align-items: center;
              justify-content: center;
              height: 30px;
              width: 30px;
              flex: 0 0 30px;
              text-align: center;
              background: #fff;
              border-radius: 50%;

              &:hover {
                background: rgb(201, 72, 74);
              }
            }
          }

          .history-item-translate {
            font-size: 14px;
            color: #999;
          }
        }
      }

      .copy-btn {
        position: absolute;
        bottom: 10px;
        left: 10px;
      }

      .source-lang,
      .translation {
        width: 100%;
        height: 100%;
        padding: 20px 30px 20px 20px;
        font-size: 20px;
        color: #1d2129;
        border-right: 1px solid #f2f3f5;

        ::v-deep(.el-textarea__inner) {
          height: 100%;
          padding: 0;
          color: #1d2129;
          border: none;
          outline: none;
          box-shadow: none;
        }
      }

      .translation {
        padding-bottom: 50px;
      }

      .icon-clear {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        cursor: pointer;
        border-radius: 50%;

        &:hover {
          background: #dbe0e7;
        }
      }
    }
  }
}
</style>
