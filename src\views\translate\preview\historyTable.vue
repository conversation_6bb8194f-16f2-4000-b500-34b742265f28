<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :width="1200">
    <div class="question-table">
      <el-form class="-mb-15px" :model="queryParams" ref="historyQueryFormRef" :inline="true" label-width="110px">
        <el-form-item :label="t('translate.history.searchOriginal')" prop="origin">
          <el-input v-model="queryParams.origin" :placeholder="t('translate.history.enterOriginal')" clearable @keyup.enter="handleQuery" class="!w-240px" />
        </el-form-item>
        <el-form-item :label="t('translate.history.searchTranslation')" prop="translation">
          <el-input v-model="queryParams.translation" :placeholder="t('translate.history.enterModifiedTranslation')" clearable @keyup.enter="handleQuery" class="!w-240px" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" />{{ t('translate.history.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" />{{ t('translate.history.reset') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column :label="t('translate.history.serialNumber')" align="center" width="90px">
          <template #default="scope">
            {{ (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column :label="t('translate.history.original')" align="center" prop="origin" />
        <el-table-column :label="t('translate.history.translation')" align="center" prop="translation" />
        <!-- <el-table-column :label="t('translate.history.modifiedOriginal')" align="center" prop="originChange"
          ><template #default="scope">
            {{ scope.row.originChange || "-" }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column :label="t('translate.history.modifiedTranslation')" align="center" prop="translationChange">
          <template #default="scope">
            {{ scope.row.translationChange || "-" }}
          </template>
        </el-table-column> -->
        <el-table-column :label="t('translate.history.remark')" align="center" prop="remark" />
        <el-table-column :label="t('translate.history.creator')" align="center" prop="createName" />
        <el-table-column :label="t('translate.history.auditor')" align="center" prop="auditName" />
        <el-table-column :label="t('translate.history.auditStatus')" align="center" prop="auditStatus" width="100">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.REPORT_AUDIT_STATUS" :value="scope.row.auditStatus || '0'" />
          </template>
        </el-table-column>
        <el-table-column :label="t('translate.history.createTime')" align="center" width="170">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('translate.history.actions')" align="center" min-width="120px">
          <template #default="scope">
            <el-button link type="primary" @click="handleEdit(scope.row.id)" v-if="scope.row.auditStatus !== 1" v-hasPermi="['system:change:update']"> {{ t('translate.history.edit') }} </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)" v-if="scope.row.auditStatus !== 1" v-hasPermi="['system:change:delete']"> {{ t('translate.history.delete') }} </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
  </Dialog>
</template>
<script lang="ts" name="AuditForm" setup>
import { DocChangeApi } from "@/api/system/change";
import { ElMessageBox } from "element-plus";
import type { Action } from "element-plus";
import { formatDate } from "@/utils/formatTime";
import { useEmit } from "@/hooks/web/useEmitt";
import { DICT_TYPE, getDictOptions } from "@/utils/dict";
import { useI18n } from 'vue-i18n';

defineOptions({ name: "HistoryTable" });

const { t } = useI18n();
const emit = defineEmits(["edit", "toShowDetailTable"]);

const dialogVisible = ref(false); // 弹窗的是否展示
const dialogTitle = ref(computed(() => t('translate.history.problemReport'))); // 弹窗的标题

const router = useRouter();
const message = useMessage(); // 消息弹窗
const loading = ref(true); // 列表的加载中
const list = ref<any[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const queryParams = reactive({
  transId: 0,
  pageNo: 1,
  pageSize: 10,
  origin: "",
  translation: "",
});
const historyQueryFormRef = ref();

/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await DocChangeApi.getDocChangePage(queryParams);
    list.value = data.list;
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true;
  queryParams.transId = id;
  await getList();
};
defineExpose({ open }); // 提供 open 方法，用于打开弹窗

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  historyQueryFormRef.value?.resetFields();
  handleQuery();
};

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    await DocChangeApi.deleteDocChange(id);
    message.success(t('translate.history.deleteSuccess'));
    // 刷新列表
    await getList();
  } catch {}
};

/** 确认审核按钮操作 */
const handleCheck = async (id: number) => {
  try {
    // 审核二次确认
    ElMessageBox.confirm(t('translate.history.confirmAudit'), t('translate.history.auditInfo'), {
      confirmButtonText: t('translate.history.approve'),
      cancelButtonText: t('translate.history.reject'),
      type: "warning",
      cancelButtonClass: "!bg-red-500 !text-white",
      confirmButtonClass: "!bg-green-500",
      distinguishCancelAndClose: true,
    })
      .then(async () => {
        await DocChangeApi.auditDocChange({ id, status: 1 });
        ElMessage({
          type: "success",
          message: t('translate.history.auditApproved'),
        });
        getList();
      })
      .catch(async (action: Action) => {
        console.log(action);
        await DocChangeApi.auditDocChange({ id, status: 2 });
        action === "cancel" &&
          ElMessage({
            type: "info",
            message: t('translate.history.auditRejected'),
          });
        getList();
      });
  } catch {}
};

const handleEdit = (id: number) => {
  emit("toShowDetailTable", id);
};

// const { emitEvent } = useEmit("showEditForm");
/** 修改操作 */
// const handleEdit = (id: number) => {
// emitEvent({ id });
// };
</script>
<style lang="scss" scoped>
.question-table {
  overflow: hidden;
}
::v-deep(.el-popper) {
  max-width: 300px !important;
}
</style>
