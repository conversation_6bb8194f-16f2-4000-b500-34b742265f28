<template>
  <div class="upload-container">
    <!-- 文件已上传状态 -->
    <div class="show-file" v-if="fileList.length > 0 && !formLoading">
      <div class="file-display">
        <div class="file-info">
          <div class="file-icon">
            <img :src="getFileIcon(fileList[0].url)" alt="file" />
          </div>
          <div class="file-details">
            <p class="file-name">{{ fileList[0].name }}</p>
            <!-- <p class="file-status success">上传成功</p> -->
          </div>
        </div>
        <div class="file-actions">
          <el-button type="danger" size="small" @click="resetForm">{{ t('translate.uploadFile.delete') }}</el-button>
        </div>
      </div>
    </div>

    <!-- 上传中状态 -->
    <div class="cur-file-uploading" v-else-if="formLoading">
      <div class="percent">
        <el-progress type="dashboard" :percentage="uploadProgress" :color="colors" />
      </div>
      <p class="text">{{ t('translate.uploadFile.uploading') }}</p>
      <p class="fileName">{{ currentFileName }}</p>
    </div>

    <!-- 上传区域 -->
    <el-upload v-else class="layout-upload" ref="uploadRef" v-model:file-list="fileList" :action="uploadUrl"
      :auto-upload="false" :data="queryParams" :headers="uploadHeaders" :limit="1" :http-request="httpRequest"
      :on-error="submitFormError" :on-exceed="handleExceed" :on-change="handleChange" :on-success="submitFormSuccess"
      :on-progress="handleProgress" :before-upload="beforeFileUpload"
      :accept="limitFileType.map((type) => '.' + type).join(',')" :show-file-list="false" drag>
      <div class="show-upload">
        <div class="img-box">
          <img class="img" src="../../../assets/icons/add.png" alt="add" />
        </div>
        <div class="el-upload__text">{{ t('translate.uploadFile.dragText') }} <span class="color-blue">{{ t('translate.uploadFile.clickUpload') }}</span></div>
        <div class="el-upload__tip">
          <div class="img-line">
            <img class="img" :src="wordIcon" alt="word" />
            <p class="text">Word</p>
          </div>
          <div class="img-line">
            <img class="img" :src="pdfIcon" alt="pdf" />
            <p class="text">PDF</p>
          </div>
          <div class="img-line">
            <img class="img" :src="excelIcon" alt="excel" />
            <p class="text">Excel</p>
          </div>
          <div class="img-line">
            <img class="img" :src="pptIcon" alt="ppt" />
            <p class="text">PPT</p>
          </div>
          <div class="img-line">
            <img class="img" :src="rtfIcon" alt="rtf" />
            <p class="text">RTF</p>
          </div>
          <div class="img-line">
            <img class="img" :src="txtIcon" alt="txt" />
            <p class="text">TXT</p>
          </div>
        </div>
        <div class="upload__tip">{{ t('translate.uploadFile.supportTypes', { maxFileSize }) }}</div>
      </div>
    </el-upload>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import { UploadProps, UploadFile } from "element-plus";
import { useMessage } from "@/hooks/web/useMessage";
import { useUpload } from "@/components/UploadFile/src/useUpload";
import { useI18n } from 'vue-i18n';
import wordIcon from "../../../assets/icons/word.png";
import pdfIcon from "../../../assets/icons/pdf.png";
import excelIcon from "../../../assets/icons/excel.png";
import pptIcon from "../../../assets/icons/ppt.png";
import rtfIcon from "../../../assets/icons/rtf.png";
import txtIcon from "../../../assets/icons/txt.png";

const { uploadUrl, httpRequest } = useUpload();

defineOptions({ name: "UploadFile" });

// 定义 props
interface Props {
  modelValue?: string; // 用于回显的文件列表
  disabled?: boolean; // 是否禁用
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  disabled: false,
});

const { t } = useI18n();
const message = useMessage(); // 消息弹窗
const formLoading = ref(false); // 表单的加载中
const uploadRef = ref();
const queryParams = ref({});
const uploadHeaders = ref(); // 上传 Header 头
const fileList = ref<any[]>([]); // 文件列表
const uploadProgress = ref(0); // 上传进度
const currentFileName = ref(""); // 当前上传文件名

// 监听 props.modelValue 变化，用于回显文件列表
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      fileList.value = [
        {
          name: getFileNameFromUrl(newVal),
          url: newVal,
          status: "success",
        },
      ];
    } else {
      fileList.value = [];
    }
  },
  { immediate: true, deep: true }
);

const colors = [
  { color: "#f56c6c", percentage: 20 },
  { color: "#e6a23c", percentage: 40 },
  { color: "#5cb87a", percentage: 60 },
  { color: "#1989fa", percentage: 80 },
  { color: "#6f7ad3", percentage: 100 },
];

const limitFileType = ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "txt", "rtf"];
const maxFileSize = 100;

// 从URL中提取文件名
const getFileNameFromUrl = (url: string): string => {
  if (!url) return "";
  const parts = url.split("/");
  return parts[parts.length - 1] || "unknown";
};

// 根据文件名获取对应的图标
const getFileIcon = (url: string): string => {
  const ext = url.split(".").pop()?.toLowerCase();
  if (ext === "doc" || ext === "docx") {
    return wordIcon;
  } else if (ext === "pdf") {
    return pdfIcon;
  } else if (ext === "xls" || ext === "xlsx") {
    return excelIcon;
  } else if (ext === "ppt" || ext === "pptx") {
    return pptIcon;
  } else if (ext === "rtf") {
    return rtfIcon;
  } else if (ext === "txt") {
    return txtIcon;
  } else {
    return "";
  }
};

const beforeFileUpload: UploadProps["beforeUpload"] = (rawFile) => {
  const fileExtension = rawFile.name.split(".").pop()?.toLowerCase();
  if (!fileExtension || !limitFileType.includes(fileExtension)) {
    message.warning(t('translate.uploadFile.unsupportedFileType'));
    return false;
  }

  if (rawFile.size > 1024 * 1024 * maxFileSize) {
    message.warning(t('translate.uploadFile.fileSizeExceeded', { maxFileSize }));
    return false;
  }
  return true;
};

const handleChange: UploadProps["onChange"] = (file, fileList) => {
  if (file.status === "ready") {
    if (!beforeFileUpload(file.raw!)) {
      resetForm();
      return;
    }

    formLoading.value = true;
    currentFileName.value = file.name;
    uploadProgress.value = 0;
    queryParams.value = { path: file.name };

    // 手动触发上传
    uploadRef.value!.submit();
  }
};

// 上传过程 - 修复进度不更新问题
const handleProgress = (evt: any, uploadFile: UploadFile, uploadFiles: UploadFile[]) => {
  // 计算上传进度百分比
  const percentage = Math.round((evt.loaded / evt.total) * 100);
  uploadProgress.value = percentage;
};

/** 文件上传成功 - 修复成功后模板不显示问题 */
const emits = defineEmits(["success", "update:modelValue"]);
const submitFormSuccess = (response: any, uploadFile: UploadFile) => {
  uploadProgress.value = 100;

  setTimeout(() => {
    // 添加成功的文件到列表
    fileList.value = [
      {
        name: uploadFile.name,
        url: response?.data || "",
        status: "success",
        uid: uploadFile.uid,
      },
    ];

    emits("success", response?.data, uploadFile.name);
    emits("update:modelValue", response?.data); // 更新父组件的 v-model
    message.success(t('translate.uploadFile.uploadSuccess'));
    formLoading.value = false;
  }, 800);

  return response?.data;
};

/** 上传错误提示 */
const submitFormError = (error: any, uploadFile: UploadFile): void => {
  message.error(t('translate.uploadFile.uploadFailed'));
  formLoading.value = false;
  uploadProgress.value = 0;
  currentFileName.value = "";
};

/** 重置表单 */
const resetForm = async (): Promise<void> => {
  fileList.value = [];
  formLoading.value = false;
  uploadProgress.value = 0;
  currentFileName.value = "";
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
  emits("update:modelValue", ""); // 清空父组件的 v-model
};

/** 文件数超出提示 */
const handleExceed = (files: File[]): void => {
  message.error(t('translate.uploadFile.maxOneFile'));
};
</script>
<style lang="scss" scoped>
.upload-container {
  width: 100%;
  // min-height: 350px;
}

.layout-upload {
  height: 350px;
  width: 100%;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  transition: all 0.2s linear;
  position: relative;
  user-select: none;

  &:active,
  &:hover,
  &:focus {
    border-color: #409eff;
  }

  :deep(.el-upload) {
    width: 100%;
    height: 100%;

    .el-upload-dragger {
      border: none;
      width: 100%;
      height: 100%;
      border-radius: 6px;
    }
  }
}

// 文件已上传显示区域
.show-file {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .file-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 20px 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;

      .file-icon {
        margin-right: 16px;

        img {
          width: 48px;
          height: 48px;
        }
      }

      .file-details {
        .file-name {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin: 0 0 8px 0;
          word-break: break-all;
          max-width: 600px;
        }

        .file-status {
          font-size: 14px;
          margin: 0;

          &.success {
            color: #67c23a;
          }
        }
      }
    }

    .file-actions {
      margin-left: 16px;
    }
  }
}

// 上传中状态
.cur-file-uploading {
  width: 100%;
  height: 350px;
  border: 2px solid #409eff;
  border-radius: 6px;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;

  .percent {
    width: 120px;
    height: 120px;
    margin: 20px auto;
    display: flex;
    justify-content: center;
    align-items: center;

    :deep(.el-progress) {
      width: 100%;
      height: 100%;
    }
  }

  .text {
    font-size: 18px;
    color: #333;
    margin: 20px 0 10px 0;
    font-weight: 500;
  }

  .fileName {
    font-size: 14px;
    color: #666;
    margin: 10px 0;
    word-break: break-all;
    max-width: 300px;
  }
}

// 上传区域样式
.show-upload {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.img-box {
  margin: 20px 0;
  text-align: center;

  .img {
    vertical-align: middle;
    height: 50px;
    opacity: 0.6;
  }
}

.el-upload__text {
  text-align: center;
  font-size: 16px;
  color: #666;

  .color-blue {
    color: #409eff;
    cursor: pointer;
  }
}

.el-upload__tip {
  display: flex;
  justify-content: center;
  color: #999;
  margin: 20px 0 0 0;
  flex-wrap: wrap;

  .img-line {
    width: 60px;
    text-align: center;
    border: 1px dashed #e1e1e1;
    border-radius: 4px;
    padding: 10px 5px;
    margin: 5px 10px;
    transition: all 0.2s;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .img {
      display: block;
      margin: 0 auto;
      width: 32px;
      height: 32px;
    }

    .text {
      font-size: 12px;
      color: #999;
      margin-top: 6px;
    }
  }
}

.upload__tip {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 20px;
  line-height: 1.5;
}
</style>
