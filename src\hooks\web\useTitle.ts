import { watch, computed } from 'vue'
import { isString } from '@/utils/is'
import { getMenuTitle } from '@/utils/menuI18n'
import { useAppTitle } from '@/hooks/web/useAppTitle'

export const useTitle = (newTitle?: string) => {
  const { t } = useI18n()
  const { appTitle } = useAppTitle()

  // 使用 computed 来响应语言变化
  const title = computed(() => {
    return newTitle ? `${appTitle.value} - ${getMenuTitle(newTitle as string, t)}` : appTitle.value
  })

  watch(
    title,
    (n, o) => {
      if (isString(n) && n !== o && document) {
        document.title = n
      }
    },
    { immediate: true }
  )

  return title
}
